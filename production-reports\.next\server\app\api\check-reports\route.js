/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/check-reports/route";
exports.ids = ["app/api/check-reports/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcheck-reports%2Froute&page=%2Fapi%2Fcheck-reports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcheck-reports%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcheck-reports%2Froute&page=%2Fapi%2Fcheck-reports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcheck-reports%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_check_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/check-reports/route.ts */ \"(rsc)/./src/app/api/check-reports/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/check-reports/route\",\n        pathname: \"/api/check-reports\",\n        filename: \"route\",\n        bundlePath: \"app/api/check-reports/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\api\\\\check-reports\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_check_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjaGVjay1yZXBvcnRzJTJGcm91dGUmcGFnZT0lMkZhcGklMkZjaGVjay1yZXBvcnRzJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGY2hlY2stcmVwb3J0cyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNwcm9kdWtjamElNUNWaWRlb3MlNUNBSUJfUkFQT1JUJTVDcHJvZHVjdGlvbi1yZXBvcnRzJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNwcm9kdWtjamElNUNWaWRlb3MlNUNBSUJfUkFQT1JUJTVDcHJvZHVjdGlvbi1yZXBvcnRzJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNvRDtBQUNqSTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxccHJvZHVrY2phXFxcXFZpZGVvc1xcXFxBSUJfUkFQT1JUXFxcXHByb2R1Y3Rpb24tcmVwb3J0c1xcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxjaGVjay1yZXBvcnRzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9jaGVjay1yZXBvcnRzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvY2hlY2stcmVwb3J0c1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvY2hlY2stcmVwb3J0cy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXHByb2R1a2NqYVxcXFxWaWRlb3NcXFxcQUlCX1JBUE9SVFxcXFxwcm9kdWN0aW9uLXJlcG9ydHNcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY2hlY2stcmVwb3J0c1xcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcheck-reports%2Froute&page=%2Fapi%2Fcheck-reports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcheck-reports%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/check-reports/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/check-reports/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET() {\n    let db;\n    try {\n        const Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n        const path = __webpack_require__(/*! path */ \"path\");\n        const dbPath = path.join(process.cwd(), \"prisma\", \"baza_danych.db\");\n        db = new Database(dbPath);\n        // Get raw reports\n        const rawReports = db.prepare('SELECT * FROM raporty_produkcyjne').all();\n        // Get reports with joins (like the API does)\n        const reportsWithJoins = db.prepare(`\n      SELECT\n        r.*,\n        p.kod_handlowy as produkt_kod,\n        p.nazwa as produkt_nazwa,\n        m.nazwa as maszyna_nazwa,\n        pr.imie as pracownik_imie,\n        pr.nazwisko as pracownik_nazwisko,\n        s.nazwa as surowiec_nazwa,\n        mat.nazwa as material_nazwa\n      FROM raporty_produkcyjne r\n      LEFT JOIN produkty p ON r.produkt_id = p.id\n      LEFT JOIN maszyny m ON r.maszyna_id = m.id\n      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id\n      LEFT JOIN surowce s ON r.surowiec_id = s.id\n      LEFT JOIN materialy mat ON r.material_id = mat.id\n      ORDER BY r.data_utworzenia DESC\n    `).all();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            rawReports,\n            reportsWithJoins,\n            rawCount: rawReports.length,\n            joinedCount: reportsWithJoins.length\n        });\n    } catch (error) {\n        console.error(\"Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Error\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    } finally{\n        if (db) {\n            db.close();\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/check-reports/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcheck-reports%2Froute&page=%2Fapi%2Fcheck-reports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcheck-reports%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();