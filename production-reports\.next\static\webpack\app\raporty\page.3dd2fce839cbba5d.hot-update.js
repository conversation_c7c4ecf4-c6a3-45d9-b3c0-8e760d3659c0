"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/raporty/page",{

/***/ "(app-pages-browser)/./src/app/raporty/page.tsx":
/*!**********************************!*\
  !*** ./src/app/raporty/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RaportyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction RaportyPage() {\n    _s();\n    const [raporty, setRaporty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [produkty, setProdukty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [maszyny, setMaszyny] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pracownicy, setPracownicy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [surowce, setSurowce] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [materialy, setMaterialy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RaportyPage.useEffect\": ()=>{\n            const loadData = {\n                \"RaportyPage.useEffect.loadData\": async ()=>{\n                    console.log(\"Starting to load data...\");\n                    try {\n                        console.log(\"Fetching reports...\");\n                        await fetchRaporty();\n                        console.log(\"Fetching master data...\");\n                        await fetchMasterData();\n                        console.log(\"Data loaded successfully\");\n                    } catch (error) {\n                        console.error(\"Błąd podczas ładowania danych:\", error);\n                    } finally{\n                        console.log(\"Setting loading to false\");\n                        setLoading(false);\n                    }\n                }\n            }[\"RaportyPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"RaportyPage.useEffect\"], []);\n    const fetchRaporty = async ()=>{\n        try {\n            const response = await fetch(\"/api/raporty-direct\");\n            const data = await response.json();\n            setRaporty(data);\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania raportów:\", error);\n        }\n    };\n    const fetchMasterData = async ()=>{\n        try {\n            const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] = await Promise.all([\n                fetch(\"/api/produkty-direct\"),\n                fetch(\"/api/maszyny\"),\n                fetch(\"/api/pracownicy\"),\n                fetch(\"/api/surowce\"),\n                fetch(\"/api/materialy\")\n            ]);\n            setProdukty(await produktyRes.json());\n            setMaszyny(await maszynyRes.json());\n            setPracownicy(await pracownicyRes.json());\n            setSurowce(await surowceRes.json());\n            setMaterialy(await materialyRes.json());\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania danych:\", error);\n        }\n    };\n    const handleEdit = (raport)=>{\n        // Redirect to edit page instead of inline editing\n        window.location.href = \"/edytuj-raport/\".concat(raport.id);\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm(\"Czy na pewno chcesz usunąć ten raport?\")) return;\n        try {\n            const response = await fetch(\"/api/raporty-direct/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                await fetchRaporty();\n            } else {\n                console.error(\"Błąd podczas usuwania raportu\");\n            }\n        } catch (error) {\n            console.error(\"Błąd podczas usuwania raportu:\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-xl\",\n                children: [\n                    \"Ładowanie... (Reports: \",\n                    raporty.length,\n                    \", Products: \",\n                    produkty.length,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-full mx-auto relative z-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-dark rounded-t-2xl border border-gray-600/50 p-6 animate-slide-in-top shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center floating\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                                                    children: \"RAPORTY PRODUKCYJNE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400 font-medium\",\n                                                    children: \"Lista wszystkich raport\\xf3w\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-xl font-bold text-lg shadow-lg animate-glow\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-black\",\n                                                                children: \"AIB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"ELASTOMERY\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-red-600 to-red-700 rounded-xl blur opacity-30 -z-10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/nowy-raport\",\n                                    className: \"btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl transition-all duration-300 hover:shadow-xl font-semibold flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 4v16m8-8H4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Nowy Raport\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchRaporty,\n                                    className: \"btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white px-6 py-3 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Odśwież\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-dark border-x border-b border-gray-600/50 rounded-b-2xl shadow-2xl animate-fade-in-up\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: raporty.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16 animate-fade-in-up\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-8xl mb-4 floating\",\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-blue-500/20 rounded-full blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-2xl mb-8 font-light\",\n                                    children: \"Brak raport\\xf3w w bazie danych\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/nowy-raport\",\n                                    className: \"inline-block btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 rounded-2xl transition-all duration-300 hover:shadow-2xl font-semibold text-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Dodaj raport\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"bg-gray-700 border-b border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-left text-white font-semibold border-r border-gray-600 w-12\",\n                                                children: \"ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[120px]\",\n                                                children: \"Kod handlowy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[200px]\",\n                                                children: \"Nazwa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Nr zam\\xf3wienia\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Waga\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Metraż rolki\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-16\",\n                                                children: \"Ilość szt\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Suma metr\\xf3w\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Surowiec użyty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Szarża surowca\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Zużycie surowca\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Odpad nieużytkowy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Nr maszyny\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"ID pracownika\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Czas pracy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold w-24\",\n                                                children: \"Akcje\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"glass\",\n                                    children: raporty.map((raport, index)=>{\n                                        var _raport_waga, _raport_zuzyty_surowiec, _raport_odpad_surowiec, _pracownicy_find, _raport_czas_pracy_maszyny;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"table-row-hover border-b border-gray-700/50 transition-all duration-300 cursor-pointer hover:bg-blue-900/20 \".concat(index % 2 === 0 ? \"bg-gray-800/50\" : \"bg-gray-750/50\"),\n                                            style: {\n                                                animationDelay: \"\".concat(index * 0.1, \"s\")\n                                            },\n                                            onClick: ()=>handleEdit(raport),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-medium\",\n                                                    children: raport.id\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-3 py-2 text-white border-r border-gray-600 font-medium\",\n                                                    children: raport.produkt_kod || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-3 py-2 text-white border-r border-gray-600\",\n                                                    children: raport.produkt_nazwa || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: raport.nr_zamowienia || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_waga = raport.waga) === null || _raport_waga === void 0 ? void 0 : _raport_waga.toFixed(1)) || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: raport.metraz_rolek.toFixed(0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: raport.ilosc_rolek\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: (raport.metraz_rolek * raport.ilosc_rolek).toFixed(0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: raport.surowiec_nazwa || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: raport.szarza_surowca || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_zuzyty_surowiec = raport.zuzyty_surowiec) === null || _raport_zuzyty_surowiec === void 0 ? void 0 : _raport_zuzyty_surowiec.toFixed(1)) || \"0.0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_odpad_surowiec = raport.odpad_surowiec) === null || _raport_odpad_surowiec === void 0 ? void 0 : _raport_odpad_surowiec.toFixed(1)) || \"0.0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-600 text-white px-1 py-0.5 rounded text-xs\",\n                                                        children: raport.maszyna_nazwa || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-green-600 text-white px-1 py-0.5 rounded text-xs\",\n                                                        children: ((_pracownicy_find = pracownicy.find((p)=>p.id === raport.pracownik_id)) === null || _pracownicy_find === void 0 ? void 0 : _pracownicy_find.numer) || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_czas_pracy_maszyny = raport.czas_pracy_maszyny) === null || _raport_czas_pracy_maszyny === void 0 ? void 0 : _raport_czas_pracy_maszyny.toFixed(1)) || \"0.0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleEdit(raport);\n                                                                },\n                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                children: \"✏️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleDelete(raport.id);\n                                                                },\n                                                                className: \"bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                children: \"\\uD83D\\uDDD1️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, raport.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 glass-dark rounded-2xl border border-gray-600/50 p-6 animate-fade-in-up\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: raporty.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączna liczba raport\\xf3w\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: raporty.reduce((sum, r)=>sum + r.metraz_rolek * r.ilosc_rolek, 0).toFixed(0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączne metry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: raporty.reduce((sum, r)=>sum + r.ilosc_rolek, 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączna ilość sztuk\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400\",\n                                        children: raporty.reduce((sum, r)=>sum + r.czas_pracy_maszyny, 0).toFixed(1)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączny czas pracy (h)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex justify-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white py-3 px-6 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Menu Gł\\xf3wne\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(RaportyPage, \"EhEFf1dryrKzAxJk6kIpxQbWon8=\");\n_c = RaportyPage;\nvar _c;\n$RefreshReg$(_c, \"RaportyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/raporty/page.tsx\n"));

/***/ })

});