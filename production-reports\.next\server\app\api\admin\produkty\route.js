/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/produkty/route";
exports.ids = ["app/api/admin/produkty/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprodukty%2Froute&page=%2Fapi%2Fadmin%2Fprodukty%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprodukty%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprodukty%2Froute&page=%2Fapi%2Fadmin%2Fprodukty%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprodukty%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_admin_produkty_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/produkty/route.ts */ \"(rsc)/./src/app/api/admin/produkty/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/produkty/route\",\n        pathname: \"/api/admin/produkty\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/produkty/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\api\\\\admin\\\\produkty\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_admin_produkty_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprodukty%2Froute&page=%2Fapi%2Fadmin%2Fprodukty%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprodukty%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/produkty/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/admin/produkty/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function GET() {\n    try {\n        const produkty = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.produkty.findMany({\n            orderBy: {\n                kod_handlowy: \"asc\"\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(produkty);\n    } catch (error) {\n        console.error(\"Błąd podczas pobierania produktów:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Błąd podczas pobierania produktów\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const data = await request.json();\n        // Use direct database access to handle new material fields\n        const Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n        const path = __webpack_require__(/*! path */ \"path\");\n        const dbPath = path.join(process.cwd(), \"prisma\", \"baza_danych.db\");\n        const db = new Database(dbPath);\n        // Insert the product with all fields\n        const insertStmt = db.prepare(`\n      INSERT INTO produkty (\n        kod_handlowy, nazwa, metraz, surowiec, material, material_percent,\n        material1, material1_percent, material2, material2_percent,\n        material3, material3_percent, material4, material4_percent,\n        material5, material5_percent, info, wydajnosc\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = insertStmt.run(data.kod_handlowy || null, data.nazwa || null, data.metraz || 0, data.surowiec || null, data.material || null, data.material_percent || null, data.material1 || null, data.material1_percent || null, data.material2 || null, data.material2_percent || null, data.material3 || null, data.material3_percent || null, data.material4 || null, data.material4_percent || null, data.material5 || null, data.material5_percent || null, data.info || null, data.wydajnosc || null);\n        // Get the created product\n        const produkt = db.prepare(\"SELECT * FROM produkty WHERE id = ?\").get(result.lastInsertRowid);\n        db.close();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(produkt);\n    } catch (error) {\n        console.error(\"Błąd podczas dodawania produktu:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Błąd podczas dodawania produktu: \" + error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/produkty/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query',\n        'info',\n        'warn',\n        'error'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBSztRQUFDO1FBQVM7UUFBUTtRQUFRO0tBQVE7QUFDekMsR0FBRTtBQUVGLElBQUlDLElBQXFDLEVBQUVKLGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwcm9kdWtjamFcXFZpZGVvc1xcQUlCX1JBUE9SVFxccHJvZHVjdGlvbi1yZXBvcnRzXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcbiAgbG9nOiBbJ3F1ZXJ5JywgJ2luZm8nLCAnd2FybicsICdlcnJvciddLFxufSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprodukty%2Froute&page=%2Fapi%2Fadmin%2Fprodukty%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprodukty%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();