"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/raporty/page",{

/***/ "(app-pages-browser)/./src/app/raporty/page.tsx":
/*!**********************************!*\
  !*** ./src/app/raporty/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RaportyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction RaportyPage() {\n    _s();\n    const [raporty, setRaporty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [produkty, setProdukty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [maszyny, setMaszyny] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pracownicy, setPracownicy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [surowce, setSurowce] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [materialy, setMaterialy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RaportyPage.useEffect\": ()=>{\n            fetchRaporty();\n            fetchMasterData();\n        }\n    }[\"RaportyPage.useEffect\"], []);\n    const fetchRaporty = async ()=>{\n        try {\n            const response = await fetch(\"/api/raporty-direct\");\n            const data = await response.json();\n            setRaporty(data);\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania raportów:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchMasterData = async ()=>{\n        try {\n            const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] = await Promise.all([\n                fetch(\"/api/produkty-direct\"),\n                fetch(\"/api/maszyny\"),\n                fetch(\"/api/pracownicy\"),\n                fetch(\"/api/surowce\"),\n                fetch(\"/api/materialy\")\n            ]);\n            setProdukty(await produktyRes.json());\n            setMaszyny(await maszynyRes.json());\n            setPracownicy(await pracownicyRes.json());\n            setSurowce(await surowceRes.json());\n            setMaterialy(await materialyRes.json());\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania danych:\", error);\n        }\n    };\n    const handleEdit = (raport)=>{\n        // Redirect to edit page instead of inline editing\n        window.location.href = \"/edytuj-raport/\".concat(raport.id);\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm(\"Czy na pewno chcesz usunąć ten raport?\")) return;\n        try {\n            const response = await fetch(\"/api/raporty-direct/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                await fetchRaporty();\n            } else {\n                console.error(\"Błąd podczas usuwania raportu\");\n            }\n        } catch (error) {\n            console.error(\"Błąd podczas usuwania raportu:\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    children: Array.from({\n                        length: 20\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-2 h-2 bg-blue-500 rounded-full opacity-20 animate-pulse-slow\",\n                            style: {\n                                left: \"\".concat(Math.random() * 100, \"%\"),\n                                top: \"\".concat(Math.random() * 100, \"%\"),\n                                animationDelay: \"\".concat(Math.random() * 2, \"s\")\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-dark rounded-2xl p-12 border border-blue-500/20 shadow-2xl animate-fade-in-up relative z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 border-4 border-blue-500/20 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-blue-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-2 left-2 w-12 h-12 border-4 border-transparent border-t-purple-500 rounded-full animate-spin\",\n                                        style: {\n                                            animationDirection: \"reverse\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white mb-2 animate-shimmer\",\n                                        children: \"Ładowanie raport\\xf3w produkcyjnych...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-400 animate-pulse\",\n                                        children: \"Przygotowywanie danych\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 h-2 bg-gray-700 rounded-full overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-shimmer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-10 left-10 w-32 h-32 bg-blue-500/5 rounded-full blur-xl animate-pulse-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 w-24 h-24 bg-purple-500/5 rounded-full blur-xl animate-pulse-slow\",\n                        style: {\n                            animationDelay: \"1s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-1/4 w-40 h-40 bg-cyan-500/5 rounded-full blur-xl animate-pulse-slow\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-full mx-auto relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-dark rounded-t-2xl border border-gray-600/50 p-6 animate-slide-in-top shadow-2xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center floating\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6 text-white\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                                                        children: \"RAPORTY PRODUKCYJNE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-400 font-medium\",\n                                                        children: \"Lista wszystkich raport\\xf3w\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-xl font-bold text-lg shadow-lg animate-glow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-black\",\n                                                                    children: \"AIB\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"ELASTOMERY\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-red-600 to-red-700 rounded-xl blur opacity-30 -z-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/nowy-raport\",\n                                        className: \"btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl transition-all duration-300 hover:shadow-xl font-semibold flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Nowy Raport\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: fetchRaporty,\n                                        className: \"btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white px-6 py-3 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Odśwież\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-dark border-x border-b border-gray-600/50 rounded-b-2xl shadow-2xl animate-fade-in-up\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: raporty.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-16 animate-fade-in-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-8xl mb-4 floating\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-blue-500/20 rounded-full blur-3xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 text-2xl mb-8 font-light\",\n                                        children: \"Brak raport\\xf3w w bazie danych\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/nowy-raport\",\n                                        className: \"inline-block btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 rounded-2xl transition-all duration-300 hover:shadow-2xl font-semibold text-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 4v16m8-8H4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Dodaj raport\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-gray-700 border-b border-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-left text-white font-semibold border-r border-gray-600 w-12\",\n                                                    children: \"ID\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[120px]\",\n                                                    children: \"Kod handlowy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[200px]\",\n                                                    children: \"Nazwa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                    children: \"Nr zam\\xf3wienia\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"Waga\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"Metraż rolki\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-16\",\n                                                    children: \"Ilość szt\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"Suma metr\\xf3w\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                    children: \"Surowiec użyty\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                    children: \"Szarża surowca\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                    children: \"Zużycie surowca\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                    children: \"Odpad nieużytkowy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"Nr maszyny\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"ID pracownika\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"Czas pracy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold w-24\",\n                                                    children: \"Akcje\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"glass\",\n                                        children: raporty.map((raport, index)=>{\n                                            var _raport_waga, _raport_zuzyty_surowiec, _raport_odpad_surowiec, _pracownicy_find, _raport_czas_pracy_maszyny;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"table-row-hover border-b border-gray-700/50 transition-all duration-300 cursor-pointer hover:bg-blue-900/20 \".concat(index % 2 === 0 ? \"bg-gray-800/50\" : \"bg-gray-750/50\", \" \").concat(editingId === raport.id ? \"bg-blue-900/30\" : \"\"),\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 0.1, \"s\")\n                                                },\n                                                onClick: ()=>handleEdit(raport),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-medium\",\n                                                        children: raport.id\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-3 py-2 text-white border-r border-gray-600 font-medium\",\n                                                        children: raport.produkt_kod || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-3 py-2 text-white border-r border-gray-600\",\n                                                        children: raport.produkt_nazwa || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                        children: raport.nr_zamowienia || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: ((_raport_waga = raport.waga) === null || _raport_waga === void 0 ? void 0 : _raport_waga.toFixed(1)) || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: raport.metraz_rolek.toFixed(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: raport.ilosc_rolek\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: (raport.metraz_rolek * raport.ilosc_rolek).toFixed(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                        children: raport.surowiec_nazwa || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                        children: raport.szarza_surowca || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: ((_raport_zuzyty_surowiec = raport.zuzyty_surowiec) === null || _raport_zuzyty_surowiec === void 0 ? void 0 : _raport_zuzyty_surowiec.toFixed(1)) || \"0.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: ((_raport_odpad_surowiec = raport.odpad_surowiec) === null || _raport_odpad_surowiec === void 0 ? void 0 : _raport_odpad_surowiec.toFixed(1)) || \"0.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-blue-600 text-white px-1 py-0.5 rounded text-xs\",\n                                                            children: raport.maszyna_nazwa || \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-green-600 text-white px-1 py-0.5 rounded text-xs\",\n                                                            children: ((_pracownicy_find = pracownicy.find((p)=>p.id === raport.pracownik_id)) === null || _pracownicy_find === void 0 ? void 0 : _pracownicy_find.numer) || \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: ((_raport_czas_pracy_maszyny = raport.czas_pracy_maszyny) === null || _raport_czas_pracy_maszyny === void 0 ? void 0 : _raport_czas_pracy_maszyny.toFixed(1)) || \"0.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleEdit(raport);\n                                                                    },\n                                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                    children: \"✏️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleDelete(raport.id);\n                                                                    },\n                                                                    className: \"bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                    children: \"\\uD83D\\uDDD1️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, raport.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 glass-dark rounded-2xl border border-gray-600/50 p-6 animate-fade-in-up\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-400\",\n                                            children: raporty.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Łączna liczba raport\\xf3w\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-400\",\n                                            children: raporty.reduce((sum, r)=>sum + r.metraz_rolek * r.ilosc_rolek, 0).toFixed(0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Łączne metry\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-400\",\n                                            children: raporty.reduce((sum, r)=>sum + r.ilosc_rolek, 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Łączna ilość sztuk\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-400\",\n                                            children: raporty.reduce((sum, r)=>sum + r.czas_pracy_maszyny, 0).toFixed(1)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Łączny czas pracy (h)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 flex justify-center space-x-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white py-3 px-6 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Menu Gł\\xf3wne\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n_s(RaportyPage, \"EhEFf1dryrKzAxJk6kIpxQbWon8=\");\n_c = RaportyPage;\nvar _c;\n$RefreshReg$(_c, \"RaportyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/raporty/page.tsx\n"));

/***/ })

});