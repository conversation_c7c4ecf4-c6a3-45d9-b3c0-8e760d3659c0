"use client";

import { useState, useEffect } from "react";
import Link from "next/link";

interface RaportProdukcyjny {
  id: number;
  data_utworzenia: string;
  produkt_id: number;
  metraz_rolek: number;
  ilosc_rolek: number;
  nr_zamowienia: string | null;
  waga: number | null;
  zuzyty_surowiec: number;
  odpad_surowiec: number;
  szarza_surowca: string | null;
  zuzyty_material: number;
  odpad_material: number;
  czas_pracy_maszyny: number;
  czas_pracy_pracownika: number;
  uwagi: string | null;
  surowiec_id: number;
  material_id: number;
  maszyna_id: number;
  pracownik_id: number;
  // Related data from joins
  produkt_kod: string | null;
  produkt_nazwa: string | null;
  maszyna_nazwa: string | null;
  pracownik_imie: string | null;
  pracownik_nazwisko: string | null;
  surowiec_nazwa: string | null;
  material_nazwa: string | null;
}

interface Produkt {
  id: number;
  kod_handlowy: string | null;
  nazwa: string | null;
  metraz: number;
  info: string | null;
}

interface Maszyna {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

interface Pracownik {
  id: number;
  numer: string;
  imie: string | null;
  nazwisko: string | null;
  stanowisko: string | null;
}

interface Surowiec {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

interface Material {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

export default function RaportyPage() {
  const [raporty, setRaporty] = useState<RaportProdukcyjny[]>([]);
  const [loading, setLoading] = useState(true);

  const [produkty, setProdukty] = useState<Produkt[]>([]);
  const [maszyny, setMaszyny] = useState<Maszyna[]>([]);
  const [pracownicy, setPracownicy] = useState<Pracownik[]>([]);
  const [surowce, setSurowce] = useState<Surowiec[]>([]);
  const [materialy, setMaterialy] = useState<Material[]>([]);

  useEffect(() => {
    const loadData = async () => {
      try {
        await Promise.all([fetchRaporty(), fetchMasterData()]);
      } catch (error) {
        console.error("Błąd podczas ładowania danych:", error);
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, []);

  const fetchRaporty = async () => {
    try {
      const response = await fetch(`/api/raporty-direct`);
      const data = await response.json();
      setRaporty(data);
    } catch (error) {
      console.error("Błąd podczas ładowania raportów:", error);
    }
  };

  const fetchMasterData = async () => {
    try {
      const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] =
        await Promise.all([
          fetch("/api/produkty-direct"),
          fetch("/api/maszyny"),
          fetch("/api/pracownicy"),
          fetch("/api/surowce"),
          fetch("/api/materialy"),
        ]);

      setProdukty(await produktyRes.json());
      setMaszyny(await maszynyRes.json());
      setPracownicy(await pracownicyRes.json());
      setSurowce(await surowceRes.json());
      setMaterialy(await materialyRes.json());
    } catch (error) {
      console.error("Błąd podczas ładowania danych:", error);
    }
  };

  const handleEdit = (raport: RaportProdukcyjny) => {
    // Redirect to edit page instead of inline editing
    window.location.href = `/edytuj-raport/${raport.id}`;
  };

  const handleDelete = async (id: number) => {
    if (!confirm("Czy na pewno chcesz usunąć ten raport?")) return;

    try {
      const response = await fetch(`/api/raporty-direct/${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        await fetchRaporty();
      } else {
        console.error("Błąd podczas usuwania raportu");
      }
    } catch (error) {
      console.error("Błąd podczas usuwania raportu:", error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-xl">
          Ładowanie... (Reports: {raporty.length}, Products: {produkty.length})
          <br />
          <div className="text-sm mt-2">
            Loading state: {loading ? "true" : "false"}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4">
      {/* Simplified background */}

      <div className="max-w-full mx-auto relative z-10">
        {/* Header Section with modern styling */}
        <div className="glass-dark rounded-t-2xl border border-gray-600/50 p-6 animate-slide-in-top shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-6">
              {/* Animated icon */}
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center floating">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
              </div>

              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                  RAPORTY PRODUKCYJNE
                </h1>
                <div className="text-blue-400 font-medium">
                  Lista wszystkich raportów
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Company logo with glow effect */}
              <div className="relative">
                <div className="bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-xl font-bold text-lg shadow-lg animate-glow">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <span className="text-sm font-black">AIB</span>
                    </div>
                    <span>ELASTOMERY</span>
                  </div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-red-600 to-red-700 rounded-xl blur opacity-30 -z-10"></div>
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-4">
            <Link
              href="/nowy-raport"
              className="btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl transition-all duration-300 hover:shadow-xl font-semibold flex items-center space-x-2"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              <span>Nowy Raport</span>
            </Link>
            <button
              onClick={fetchRaporty}
              className="btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white px-6 py-3 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              <span>Odśwież</span>
            </button>
          </div>
        </div>

        {/* Main Content Area with Table */}
        <div className="glass-dark border-x border-b border-gray-600/50 rounded-b-2xl shadow-2xl animate-fade-in-up">
          <div className="overflow-x-auto">
            {/* Debug: raporty.length = {raporty.length} */}
            {raporty.length === 0 ? (
              <div className="text-center py-16 animate-fade-in-up">
                <div className="relative mb-8">
                  <div className="text-8xl mb-4 floating">📊</div>
                  <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-3xl"></div>
                </div>
                <p className="text-gray-300 text-2xl mb-8 font-light">
                  Brak raportów w bazie danych
                </p>
                <Link
                  href="/nowy-raport"
                  className="inline-block btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 rounded-2xl transition-all duration-300 hover:shadow-2xl font-semibold text-lg"
                >
                  <span className="flex items-center space-x-2">
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4v16m8-8H4"
                      />
                    </svg>
                    <span>Dodaj raport</span>
                  </span>
                </Link>
              </div>
            ) : (
              <table className="w-full text-sm">
                <thead>
                  <tr className="bg-gray-700 border-b border-gray-600">
                    <th className="px-2 py-3 text-left text-white font-semibold border-r border-gray-600 w-12">
                      ID
                    </th>
                    <th className="px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[120px]">
                      Kod handlowy
                    </th>
                    <th className="px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[200px]">
                      Nazwa
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24">
                      Nr zamówienia
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Waga
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Metraż rolki
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-16">
                      Ilość szt
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Suma metrów
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24">
                      Surowiec użyty
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24">
                      Szarża surowca
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24">
                      Zużycie surowca
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24">
                      Odpad nieużytkowy
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Nr maszyny
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      ID pracownika
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20">
                      Czas pracy
                    </th>
                    <th className="px-2 py-3 text-center text-white font-semibold w-24">
                      Akcje
                    </th>
                  </tr>
                </thead>
                <tbody className="glass">
                  {raporty.map((raport, index) => (
                    <tr
                      key={raport.id}
                      className={`table-row-hover border-b border-gray-700/50 transition-all duration-300 cursor-pointer hover:bg-blue-900/20 ${
                        index % 2 === 0 ? "bg-gray-800/50" : "bg-gray-750/50"
                      }`}
                      style={{ animationDelay: `${index * 0.1}s` }}
                      onClick={() => handleEdit(raport)}
                    >
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-medium">
                        {raport.id}
                      </td>
                      <td className="px-3 py-2 text-white border-r border-gray-600 font-medium">
                        {raport.produkt_kod || "-"}
                      </td>
                      <td className="px-3 py-2 text-white border-r border-gray-600">
                        {raport.produkt_nazwa || "-"}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                        {raport.nr_zamowienia || "-"}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.waga?.toFixed(1) || "-"}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.metraz_rolek.toFixed(0)}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.ilosc_rolek}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {(raport.metraz_rolek * raport.ilosc_rolek).toFixed(0)}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                        {raport.surowiec_nazwa || "-"}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                        {raport.szarza_surowca || "-"}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.zuzyty_surowiec?.toFixed(1) || "0.0"}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.odpad_surowiec?.toFixed(1) || "0.0"}
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                        <span className="bg-blue-600 text-white px-1 py-0.5 rounded text-xs">
                          {raport.maszyna_nazwa || "-"}
                        </span>
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                        <span className="bg-green-600 text-white px-1 py-0.5 rounded text-xs">
                          {pracownicy.find((p) => p.id === raport.pracownik_id)
                            ?.numer || "-"}
                        </span>
                      </td>
                      <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                        {raport.czas_pracy_maszyny?.toFixed(1) || "0.0"}
                      </td>
                      <td className="px-2 py-2 text-center text-white">
                        <div className="flex space-x-1">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEdit(raport);
                            }}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors"
                          >
                            ✏️
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(raport.id);
                            }}
                            className="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs transition-colors"
                          >
                            🗑️
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>

        {/* Summary section */}
        <div className="mt-6 glass-dark rounded-2xl border border-gray-600/50 p-6 animate-fade-in-up">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                {raporty.length}
              </div>
              <div className="text-gray-300 text-sm">
                Łączna liczba raportów
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {raporty
                  .reduce((sum, r) => sum + r.metraz_rolek * r.ilosc_rolek, 0)
                  .toFixed(0)}
              </div>
              <div className="text-gray-300 text-sm">Łączne metry</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">
                {raporty.reduce((sum, r) => sum + r.ilosc_rolek, 0)}
              </div>
              <div className="text-gray-300 text-sm">Łączna ilość sztuk</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-400">
                {raporty
                  .reduce((sum, r) => sum + r.czas_pracy_maszyny, 0)
                  .toFixed(1)}
              </div>
              <div className="text-gray-300 text-sm">Łączny czas pracy (h)</div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-6 flex justify-center space-x-4">
          <Link
            href="/"
            className="btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white py-3 px-6 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            <span>Menu Główne</span>
          </Link>
        </div>
      </div>
    </div>
  );
}
