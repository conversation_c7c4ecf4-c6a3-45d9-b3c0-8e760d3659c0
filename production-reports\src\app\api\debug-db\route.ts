import { NextResponse } from "next/server";

export async function GET() {
  let db;

  try {
    const Database = require("better-sqlite3");
    const path = require("path");

    const dbPath = path.join(process.cwd(), "baza_danych.db");
    db = new Database(dbPath);

    // Get raw report data
    const rawReport = db.prepare('SELECT * FROM raporty_produkcyjne WHERE id = 5').get();
    
    // Get all surowce
    const surowce = db.prepare('SELECT * FROM surowce').all();
    
    // Get all maszyny
    const maszyny = db.prepare('SELECT * FROM maszyny').all();
    
    // Get report with joins (like the main API)
    const reportWithJoins = db.prepare(`
      SELECT
        r.*,
        p.kod_handlowy as produkt_kod,
        p.nazwa as produkt_nazwa,
        m.nazwa as maszyna_nazwa,
        pr.imie as pracownik_imie,
        pr.naz<PERSON><PERSON> as pracownik_nazwisko,
        s.nazwa as surowiec_nazwa,
        mat.nazwa as material_nazwa
      FROM raporty_produkcyjne r
      LEFT JOIN produkty p ON r.produkt_id = p.id
      LEFT JOIN maszyny m ON r.maszyna_id = m.id
      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id
      LEFT JOIN surowce s ON r.surowiec_id = s.id
      LEFT JOIN materialy mat ON r.material_id = mat.id
      WHERE r.id = 5
    `).get();

    return NextResponse.json({
      rawReport,
      surowce,
      maszyny,
      reportWithJoins
    });
  } catch (error) {
    console.error("Debug error:", error);
    return NextResponse.json(
      { error: "Debug error", details: error.message },
      { status: 500 }
    );
  } finally {
    if (db) {
      db.close();
    }
  }
}
