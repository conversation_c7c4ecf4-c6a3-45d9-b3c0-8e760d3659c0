"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/raporty/page",{

/***/ "(app-pages-browser)/./src/app/raporty/page.tsx":
/*!**********************************!*\
  !*** ./src/app/raporty/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RaportyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction RaportyPage() {\n    _s();\n    const [raporty, setRaporty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Temporarily disable loading to test\n    const [produkty, setProdukty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [maszyny, setMaszyny] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pracownicy, setPracownicy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [surowce, setSurowce] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [materialy, setMaterialy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RaportyPage.useEffect\": ()=>{\n            const loadData = {\n                \"RaportyPage.useEffect.loadData\": async ()=>{\n                    try {\n                        await Promise.all([\n                            fetchRaporty(),\n                            fetchMasterData()\n                        ]);\n                    } catch (error) {\n                        console.error(\"Błąd podczas ładowania danych:\", error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"RaportyPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"RaportyPage.useEffect\"], []);\n    const fetchRaporty = async ()=>{\n        try {\n            const response = await fetch(\"/api/raporty-direct\");\n            const data = await response.json();\n            setRaporty(data);\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania raportów:\", error);\n        }\n    };\n    const fetchMasterData = async ()=>{\n        try {\n            const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] = await Promise.all([\n                fetch(\"/api/produkty-direct\"),\n                fetch(\"/api/maszyny\"),\n                fetch(\"/api/pracownicy\"),\n                fetch(\"/api/surowce\"),\n                fetch(\"/api/materialy\")\n            ]);\n            setProdukty(await produktyRes.json());\n            setMaszyny(await maszynyRes.json());\n            setPracownicy(await pracownicyRes.json());\n            setSurowce(await surowceRes.json());\n            setMaterialy(await materialyRes.json());\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania danych:\", error);\n        }\n    };\n    const handleEdit = (raport)=>{\n        // Redirect to edit page instead of inline editing\n        window.location.href = \"/edytuj-raport/\".concat(raport.id);\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm(\"Czy na pewno chcesz usunąć ten raport?\")) return;\n        try {\n            const response = await fetch(\"/api/raporty-direct/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                await fetchRaporty();\n            } else {\n                console.error(\"Błąd podczas usuwania raportu\");\n            }\n        } catch (error) {\n            console.error(\"Błąd podczas usuwania raportu:\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-xl\",\n                children: [\n                    \"Ładowanie... (Reports: \",\n                    raporty.length,\n                    \", Products: \",\n                    produkty.length,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-full mx-auto relative z-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-dark rounded-t-2xl border border-gray-600/50 p-6 animate-slide-in-top shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center floating\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                                                    children: \"RAPORTY PRODUKCYJNE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400 font-medium\",\n                                                    children: \"Lista wszystkich raport\\xf3w\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-xl font-bold text-lg shadow-lg animate-glow\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-black\",\n                                                                children: \"AIB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"ELASTOMERY\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-red-600 to-red-700 rounded-xl blur opacity-30 -z-10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/nowy-raport\",\n                                    className: \"btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl transition-all duration-300 hover:shadow-xl font-semibold flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 4v16m8-8H4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Nowy Raport\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchRaporty,\n                                    className: \"btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white px-6 py-3 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Odśwież\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-dark border-x border-b border-gray-600/50 rounded-b-2xl shadow-2xl animate-fade-in-up\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: raporty.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16 animate-fade-in-up\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-8xl mb-4 floating\",\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-blue-500/20 rounded-full blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-2xl mb-8 font-light\",\n                                    children: \"Brak raport\\xf3w w bazie danych\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/nowy-raport\",\n                                    className: \"inline-block btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 rounded-2xl transition-all duration-300 hover:shadow-2xl font-semibold text-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Dodaj raport\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"bg-gray-700 border-b border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-left text-white font-semibold border-r border-gray-600 w-12\",\n                                                children: \"ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[120px]\",\n                                                children: \"Kod handlowy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[200px]\",\n                                                children: \"Nazwa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Nr zam\\xf3wienia\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Waga\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Metraż rolki\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-16\",\n                                                children: \"Ilość szt\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Suma metr\\xf3w\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Surowiec użyty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Szarża surowca\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Zużycie surowca\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Odpad nieużytkowy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Nr maszyny\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"ID pracownika\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Czas pracy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold w-24\",\n                                                children: \"Akcje\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"glass\",\n                                    children: raporty.map((raport, index)=>{\n                                        var _raport_waga, _raport_zuzyty_surowiec, _raport_odpad_surowiec, _pracownicy_find, _raport_czas_pracy_maszyny;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"table-row-hover border-b border-gray-700/50 transition-all duration-300 cursor-pointer hover:bg-blue-900/20 \".concat(index % 2 === 0 ? \"bg-gray-800/50\" : \"bg-gray-750/50\"),\n                                            style: {\n                                                animationDelay: \"\".concat(index * 0.1, \"s\")\n                                            },\n                                            onClick: ()=>handleEdit(raport),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-medium\",\n                                                    children: raport.id\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-3 py-2 text-white border-r border-gray-600 font-medium\",\n                                                    children: raport.produkt_kod || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-3 py-2 text-white border-r border-gray-600\",\n                                                    children: raport.produkt_nazwa || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: raport.nr_zamowienia || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_waga = raport.waga) === null || _raport_waga === void 0 ? void 0 : _raport_waga.toFixed(1)) || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: raport.metraz_rolek.toFixed(0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: raport.ilosc_rolek\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: (raport.metraz_rolek * raport.ilosc_rolek).toFixed(0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: raport.surowiec_nazwa || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: raport.szarza_surowca || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_zuzyty_surowiec = raport.zuzyty_surowiec) === null || _raport_zuzyty_surowiec === void 0 ? void 0 : _raport_zuzyty_surowiec.toFixed(1)) || \"0.0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_odpad_surowiec = raport.odpad_surowiec) === null || _raport_odpad_surowiec === void 0 ? void 0 : _raport_odpad_surowiec.toFixed(1)) || \"0.0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-600 text-white px-1 py-0.5 rounded text-xs\",\n                                                        children: raport.maszyna_nazwa || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-green-600 text-white px-1 py-0.5 rounded text-xs\",\n                                                        children: ((_pracownicy_find = pracownicy.find((p)=>p.id === raport.pracownik_id)) === null || _pracownicy_find === void 0 ? void 0 : _pracownicy_find.numer) || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_czas_pracy_maszyny = raport.czas_pracy_maszyny) === null || _raport_czas_pracy_maszyny === void 0 ? void 0 : _raport_czas_pracy_maszyny.toFixed(1)) || \"0.0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleEdit(raport);\n                                                                },\n                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                children: \"✏️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleDelete(raport.id);\n                                                                },\n                                                                className: \"bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                children: \"\\uD83D\\uDDD1️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, raport.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 glass-dark rounded-2xl border border-gray-600/50 p-6 animate-fade-in-up\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: raporty.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączna liczba raport\\xf3w\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: raporty.reduce((sum, r)=>sum + r.metraz_rolek * r.ilosc_rolek, 0).toFixed(0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączne metry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: raporty.reduce((sum, r)=>sum + r.ilosc_rolek, 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączna ilość sztuk\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400\",\n                                        children: raporty.reduce((sum, r)=>sum + r.czas_pracy_maszyny, 0).toFixed(1)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączny czas pracy (h)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex justify-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white py-3 px-6 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Menu Gł\\xf3wne\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n            lineNumber: 164,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s(RaportyPage, \"4letIT1ZdK4pesoa26rLZxyIigE=\");\n_c = RaportyPage;\nvar _c;\n$RefreshReg$(_c, \"RaportyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcmFwb3J0eS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUU0QztBQUNmO0FBcUVkLFNBQVNHOztJQUN0QixNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR0wsK0NBQVFBLENBQXNCLEVBQUU7SUFDOUQsTUFBTSxDQUFDTSxTQUFTQyxXQUFXLEdBQUdQLCtDQUFRQSxDQUFDLFFBQVEsc0NBQXNDO0lBRXJGLE1BQU0sQ0FBQ1EsVUFBVUMsWUFBWSxHQUFHVCwrQ0FBUUEsQ0FBWSxFQUFFO0lBQ3RELE1BQU0sQ0FBQ1UsU0FBU0MsV0FBVyxHQUFHWCwrQ0FBUUEsQ0FBWSxFQUFFO0lBQ3BELE1BQU0sQ0FBQ1ksWUFBWUMsY0FBYyxHQUFHYiwrQ0FBUUEsQ0FBYyxFQUFFO0lBQzVELE1BQU0sQ0FBQ2MsU0FBU0MsV0FBVyxHQUFHZiwrQ0FBUUEsQ0FBYSxFQUFFO0lBQ3JELE1BQU0sQ0FBQ2dCLFdBQVdDLGFBQWEsR0FBR2pCLCtDQUFRQSxDQUFhLEVBQUU7SUFFekRDLGdEQUFTQTtpQ0FBQztZQUNSLE1BQU1pQjtrREFBVztvQkFDZixJQUFJO3dCQUNGLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQzs0QkFBQ0M7NEJBQWdCQzt5QkFBa0I7b0JBQ3ZELEVBQUUsT0FBT0MsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLGtDQUFrQ0E7b0JBQ2xELFNBQVU7d0JBQ1JoQixXQUFXO29CQUNiO2dCQUNGOztZQUNBVztRQUNGO2dDQUFHLEVBQUU7SUFFTCxNQUFNRyxlQUFlO1FBQ25CLElBQUk7WUFDRixNQUFNSSxXQUFXLE1BQU1DLE1BQU87WUFDOUIsTUFBTUMsT0FBTyxNQUFNRixTQUFTRyxJQUFJO1lBQ2hDdkIsV0FBV3NCO1FBQ2IsRUFBRSxPQUFPSixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxvQ0FBb0NBO1FBQ3BEO0lBQ0Y7SUFFQSxNQUFNRCxrQkFBa0I7UUFDdEIsSUFBSTtZQUNGLE1BQU0sQ0FBQ08sYUFBYUMsWUFBWUMsZUFBZUMsWUFBWUMsYUFBYSxHQUN0RSxNQUFNZCxRQUFRQyxHQUFHLENBQUM7Z0JBQ2hCTSxNQUFNO2dCQUNOQSxNQUFNO2dCQUNOQSxNQUFNO2dCQUNOQSxNQUFNO2dCQUNOQSxNQUFNO2FBQ1A7WUFFSGpCLFlBQVksTUFBTW9CLFlBQVlELElBQUk7WUFDbENqQixXQUFXLE1BQU1tQixXQUFXRixJQUFJO1lBQ2hDZixjQUFjLE1BQU1rQixjQUFjSCxJQUFJO1lBQ3RDYixXQUFXLE1BQU1pQixXQUFXSixJQUFJO1lBQ2hDWCxhQUFhLE1BQU1nQixhQUFhTCxJQUFJO1FBQ3RDLEVBQUUsT0FBT0wsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsa0NBQWtDQTtRQUNsRDtJQUNGO0lBRUEsTUFBTVcsYUFBYSxDQUFDQztRQUNsQixrREFBa0Q7UUFDbERDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHLGtCQUE0QixPQUFWSCxPQUFPSSxFQUFFO0lBQ3BEO0lBRUEsTUFBTUMsZUFBZSxPQUFPRDtRQUMxQixJQUFJLENBQUNFLFFBQVEsMkNBQTJDO1FBRXhELElBQUk7WUFDRixNQUFNaEIsV0FBVyxNQUFNQyxNQUFNLHVCQUEwQixPQUFIYSxLQUFNO2dCQUN4REcsUUFBUTtZQUNWO1lBRUEsSUFBSWpCLFNBQVNrQixFQUFFLEVBQUU7Z0JBQ2YsTUFBTXRCO1lBQ1IsT0FBTztnQkFDTEcsUUFBUUQsS0FBSyxDQUFDO1lBQ2hCO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQ0FBa0NBO1FBQ2xEO0lBQ0Y7SUFFQSxJQUFJakIsU0FBUztRQUNYLHFCQUNFLDhEQUFDc0M7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O29CQUFxQjtvQkFDVnpDLFFBQVEwQyxNQUFNO29CQUFDO29CQUFhdEMsU0FBU3NDLE1BQU07b0JBQUM7Ozs7Ozs7Ozs7OztJQUk1RTtJQUVBLHFCQUNFLDhEQUFDRjtRQUFJQyxXQUFVO2tCQUdiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFFYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0U7d0RBQ0NGLFdBQVU7d0RBQ1ZHLE1BQUs7d0RBQ0xDLFFBQU87d0RBQ1BDLFNBQVE7a0VBRVIsNEVBQUNDOzREQUNDQyxlQUFjOzREQUNkQyxnQkFBZTs0REFDZkMsYUFBYTs0REFDYkMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJUiw4REFBQ1g7b0RBQUlDLFdBQVU7Ozs7Ozs7Ozs7OztzREFHakIsOERBQUNEOzs4REFDQyw4REFBQ1k7b0RBQUdYLFdBQVU7OERBQTJGOzs7Ozs7OERBR3pHLDhEQUFDRDtvREFBSUMsV0FBVTs4REFBNEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNL0MsOERBQUNEO29DQUFJQyxXQUFVOzhDQUViLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNiLDRFQUFDWTtnRUFBS1osV0FBVTswRUFBcUI7Ozs7Ozs7Ozs7O3NFQUV2Qyw4REFBQ1k7c0VBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUdWLDhEQUFDYjtnREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTXJCLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUMzQyxrREFBSUE7b0NBQ0hvQyxNQUFLO29DQUNMTyxXQUFVOztzREFFViw4REFBQ0U7NENBQ0NGLFdBQVU7NENBQ1ZHLE1BQUs7NENBQ0xDLFFBQU87NENBQ1BDLFNBQVE7c0RBRVIsNEVBQUNDO2dEQUNDQyxlQUFjO2dEQUNkQyxnQkFBZTtnREFDZkMsYUFBYTtnREFDYkMsR0FBRTs7Ozs7Ozs7Ozs7c0RBR04sOERBQUNFO3NEQUFLOzs7Ozs7Ozs7Ozs7OENBRVIsOERBQUNDO29DQUNDQyxTQUFTdEM7b0NBQ1R3QixXQUFVOztzREFFViw4REFBQ0U7NENBQ0NGLFdBQVU7NENBQ1ZHLE1BQUs7NENBQ0xDLFFBQU87NENBQ1BDLFNBQVE7c0RBRVIsNEVBQUNDO2dEQUNDQyxlQUFjO2dEQUNkQyxnQkFBZTtnREFDZkMsYUFBYTtnREFDYkMsR0FBRTs7Ozs7Ozs7Ozs7c0RBR04sOERBQUNFO3NEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTVosOERBQUNiO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTtrQ0FFWnpDLFFBQVEwQyxNQUFNLEtBQUssa0JBQ2xCLDhEQUFDRjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQXlCOzs7Ozs7c0RBQ3hDLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7OzhDQUVqQiw4REFBQ2U7b0NBQUVmLFdBQVU7OENBQXlDOzs7Ozs7OENBR3RELDhEQUFDM0Msa0RBQUlBO29DQUNIb0MsTUFBSztvQ0FDTE8sV0FBVTs4Q0FFViw0RUFBQ1k7d0NBQUtaLFdBQVU7OzBEQUNkLDhEQUFDRTtnREFDQ0YsV0FBVTtnREFDVkcsTUFBSztnREFDTEMsUUFBTztnREFDUEMsU0FBUTswREFFUiw0RUFBQ0M7b0RBQ0NDLGVBQWM7b0RBQ2RDLGdCQUFlO29EQUNmQyxhQUFhO29EQUNiQyxHQUFFOzs7Ozs7Ozs7OzswREFHTiw4REFBQ0U7MERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7aURBS1osOERBQUNJOzRCQUFNaEIsV0FBVTs7OENBQ2YsOERBQUNpQjs4Q0FDQyw0RUFBQ0M7d0NBQUdsQixXQUFVOzswREFDWiw4REFBQ21CO2dEQUFHbkIsV0FBVTswREFBNkU7Ozs7OzswREFHM0YsOERBQUNtQjtnREFBR25CLFdBQVU7MERBQXNGOzs7Ozs7MERBR3BHLDhEQUFDbUI7Z0RBQUduQixXQUFVOzBEQUFzRjs7Ozs7OzBEQUdwRyw4REFBQ21CO2dEQUFHbkIsV0FBVTswREFBK0U7Ozs7OzswREFHN0YsOERBQUNtQjtnREFBR25CLFdBQVU7MERBQStFOzs7Ozs7MERBRzdGLDhEQUFDbUI7Z0RBQUduQixXQUFVOzBEQUErRTs7Ozs7OzBEQUc3Riw4REFBQ21CO2dEQUFHbkIsV0FBVTswREFBK0U7Ozs7OzswREFHN0YsOERBQUNtQjtnREFBR25CLFdBQVU7MERBQStFOzs7Ozs7MERBRzdGLDhEQUFDbUI7Z0RBQUduQixXQUFVOzBEQUErRTs7Ozs7OzBEQUc3Riw4REFBQ21CO2dEQUFHbkIsV0FBVTswREFBK0U7Ozs7OzswREFHN0YsOERBQUNtQjtnREFBR25CLFdBQVU7MERBQStFOzs7Ozs7MERBRzdGLDhEQUFDbUI7Z0RBQUduQixXQUFVOzBEQUErRTs7Ozs7OzBEQUc3Riw4REFBQ21CO2dEQUFHbkIsV0FBVTswREFBK0U7Ozs7OzswREFHN0YsOERBQUNtQjtnREFBR25CLFdBQVU7MERBQStFOzs7Ozs7MERBRzdGLDhEQUFDbUI7Z0RBQUduQixXQUFVOzBEQUErRTs7Ozs7OzBEQUc3Riw4REFBQ21CO2dEQUFHbkIsV0FBVTswREFBc0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUt4RSw4REFBQ29CO29DQUFNcEIsV0FBVTs4Q0FDZHpDLFFBQVE4RCxHQUFHLENBQUMsQ0FBQy9CLFFBQVFnQzs0Q0FzQmZoQyxjQWtCQUEseUJBR0FBLHdCQVNFdkIsa0JBS0Z1Qjs2REF4REwsOERBQUM0Qjs0Q0FFQ2xCLFdBQVcsK0dBRVYsT0FEQ3NCLFFBQVEsTUFBTSxJQUFJLG1CQUFtQjs0Q0FFdkNDLE9BQU87Z0RBQUVDLGdCQUFnQixHQUFlLE9BQVpGLFFBQVEsS0FBSTs0Q0FBRzs0Q0FDM0NSLFNBQVMsSUFBTXpCLFdBQVdDOzs4REFFMUIsOERBQUNtQztvREFBR3pCLFdBQVU7OERBQ1hWLE9BQU9JLEVBQUU7Ozs7Ozs4REFFWiw4REFBQytCO29EQUFHekIsV0FBVTs4REFDWFYsT0FBT29DLFdBQVcsSUFBSTs7Ozs7OzhEQUV6Qiw4REFBQ0Q7b0RBQUd6QixXQUFVOzhEQUNYVixPQUFPcUMsYUFBYSxJQUFJOzs7Ozs7OERBRTNCLDhEQUFDRjtvREFBR3pCLFdBQVU7OERBQ1hWLE9BQU9zQyxhQUFhLElBQUk7Ozs7Ozs4REFFM0IsOERBQUNIO29EQUFHekIsV0FBVTs4REFDWFYsRUFBQUEsZUFBQUEsT0FBT3VDLElBQUksY0FBWHZDLG1DQUFBQSxhQUFhd0MsT0FBTyxDQUFDLE9BQU07Ozs7Ozs4REFFOUIsOERBQUNMO29EQUFHekIsV0FBVTs4REFDWFYsT0FBT3lDLFlBQVksQ0FBQ0QsT0FBTyxDQUFDOzs7Ozs7OERBRS9CLDhEQUFDTDtvREFBR3pCLFdBQVU7OERBQ1hWLE9BQU8wQyxXQUFXOzs7Ozs7OERBRXJCLDhEQUFDUDtvREFBR3pCLFdBQVU7OERBQ1gsQ0FBQ1YsT0FBT3lDLFlBQVksR0FBR3pDLE9BQU8wQyxXQUFXLEVBQUVGLE9BQU8sQ0FBQzs7Ozs7OzhEQUV0RCw4REFBQ0w7b0RBQUd6QixXQUFVOzhEQUNYVixPQUFPMkMsY0FBYyxJQUFJOzs7Ozs7OERBRTVCLDhEQUFDUjtvREFBR3pCLFdBQVU7OERBQ1hWLE9BQU80QyxjQUFjLElBQUk7Ozs7Ozs4REFFNUIsOERBQUNUO29EQUFHekIsV0FBVTs4REFDWFYsRUFBQUEsMEJBQUFBLE9BQU82QyxlQUFlLGNBQXRCN0MsOENBQUFBLHdCQUF3QndDLE9BQU8sQ0FBQyxPQUFNOzs7Ozs7OERBRXpDLDhEQUFDTDtvREFBR3pCLFdBQVU7OERBQ1hWLEVBQUFBLHlCQUFBQSxPQUFPOEMsY0FBYyxjQUFyQjlDLDZDQUFBQSx1QkFBdUJ3QyxPQUFPLENBQUMsT0FBTTs7Ozs7OzhEQUV4Qyw4REFBQ0w7b0RBQUd6QixXQUFVOzhEQUNaLDRFQUFDWTt3REFBS1osV0FBVTtrRUFDYlYsT0FBTytDLGFBQWEsSUFBSTs7Ozs7Ozs7Ozs7OERBRzdCLDhEQUFDWjtvREFBR3pCLFdBQVU7OERBQ1osNEVBQUNZO3dEQUFLWixXQUFVO2tFQUNiakMsRUFBQUEsbUJBQUFBLFdBQVd1RSxJQUFJLENBQUMsQ0FBQ3ZCLElBQU1BLEVBQUVyQixFQUFFLEtBQUtKLE9BQU9pRCxZQUFZLGVBQW5EeEUsdUNBQUFBLGlCQUNHeUUsS0FBSyxLQUFJOzs7Ozs7Ozs7Ozs4REFHakIsOERBQUNmO29EQUFHekIsV0FBVTs4REFDWFYsRUFBQUEsNkJBQUFBLE9BQU9tRCxrQkFBa0IsY0FBekJuRCxpREFBQUEsMkJBQTJCd0MsT0FBTyxDQUFDLE9BQU07Ozs7Ozs4REFFNUMsOERBQUNMO29EQUFHekIsV0FBVTs4REFDWiw0RUFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDYTtnRUFDQ0MsU0FBUyxDQUFDNEI7b0VBQ1JBLEVBQUVDLGVBQWU7b0VBQ2pCdEQsV0FBV0M7Z0VBQ2I7Z0VBQ0FVLFdBQVU7MEVBQ1g7Ozs7OzswRUFHRCw4REFBQ2E7Z0VBQ0NDLFNBQVMsQ0FBQzRCO29FQUNSQSxFQUFFQyxlQUFlO29FQUNqQmhELGFBQWFMLE9BQU9JLEVBQUU7Z0VBQ3hCO2dFQUNBTSxXQUFVOzBFQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MkNBMUVBVixPQUFPSSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBd0Y1Qiw4REFBQ0s7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWnpDLFFBQVEwQyxNQUFNOzs7Ozs7a0RBRWpCLDhEQUFDRjt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7OzswQ0FJekMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1p6QyxRQUNFcUYsTUFBTSxDQUFDLENBQUNDLEtBQUtDLElBQU1ELE1BQU1DLEVBQUVmLFlBQVksR0FBR2UsRUFBRWQsV0FBVyxFQUFFLEdBQ3pERixPQUFPLENBQUM7Ozs7OztrREFFYiw4REFBQy9CO3dDQUFJQyxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7OzBDQUV6Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWnpDLFFBQVFxRixNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsSUFBTUQsTUFBTUMsRUFBRWQsV0FBVyxFQUFFOzs7Ozs7a0RBRW5ELDhEQUFDakM7d0NBQUlDLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7MENBRXpDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNaekMsUUFDRXFGLE1BQU0sQ0FBQyxDQUFDQyxLQUFLQyxJQUFNRCxNQUFNQyxFQUFFTCxrQkFBa0IsRUFBRSxHQUMvQ1gsT0FBTyxDQUFDOzs7Ozs7a0RBRWIsOERBQUMvQjt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU03Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUMzQyxrREFBSUE7d0JBQ0hvQyxNQUFLO3dCQUNMTyxXQUFVOzswQ0FFViw4REFBQ0U7Z0NBQ0NGLFdBQVU7Z0NBQ1ZHLE1BQUs7Z0NBQ0xDLFFBQU87Z0NBQ1BDLFNBQVE7MENBRVIsNEVBQUNDO29DQUNDQyxlQUFjO29DQUNkQyxnQkFBZTtvQ0FDZkMsYUFBYTtvQ0FDYkMsR0FBRTs7Ozs7Ozs7Ozs7MENBR04sOERBQUNFOzBDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWxCO0dBemF3QnREO0tBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb2R1a2NqYVxcVmlkZW9zXFxBSUJfUkFQT1JUXFxwcm9kdWN0aW9uLXJlcG9ydHNcXHNyY1xcYXBwXFxyYXBvcnR5XFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xuXG5pbnRlcmZhY2UgUmFwb3J0UHJvZHVrY3lqbnkge1xuICBpZDogbnVtYmVyO1xuICBkYXRhX3V0d29yemVuaWE6IHN0cmluZztcbiAgcHJvZHVrdF9pZDogbnVtYmVyO1xuICBtZXRyYXpfcm9sZWs6IG51bWJlcjtcbiAgaWxvc2Nfcm9sZWs6IG51bWJlcjtcbiAgbnJfemFtb3dpZW5pYTogc3RyaW5nIHwgbnVsbDtcbiAgd2FnYTogbnVtYmVyIHwgbnVsbDtcbiAgenV6eXR5X3N1cm93aWVjOiBudW1iZXI7XG4gIG9kcGFkX3N1cm93aWVjOiBudW1iZXI7XG4gIHN6YXJ6YV9zdXJvd2NhOiBzdHJpbmcgfCBudWxsO1xuICB6dXp5dHlfbWF0ZXJpYWw6IG51bWJlcjtcbiAgb2RwYWRfbWF0ZXJpYWw6IG51bWJlcjtcbiAgY3phc19wcmFjeV9tYXN6eW55OiBudW1iZXI7XG4gIGN6YXNfcHJhY3lfcHJhY293bmlrYTogbnVtYmVyO1xuICB1d2FnaTogc3RyaW5nIHwgbnVsbDtcbiAgc3Vyb3dpZWNfaWQ6IG51bWJlcjtcbiAgbWF0ZXJpYWxfaWQ6IG51bWJlcjtcbiAgbWFzenluYV9pZDogbnVtYmVyO1xuICBwcmFjb3duaWtfaWQ6IG51bWJlcjtcbiAgLy8gUmVsYXRlZCBkYXRhIGZyb20gam9pbnNcbiAgcHJvZHVrdF9rb2Q6IHN0cmluZyB8IG51bGw7XG4gIHByb2R1a3RfbmF6d2E6IHN0cmluZyB8IG51bGw7XG4gIG1hc3p5bmFfbmF6d2E6IHN0cmluZyB8IG51bGw7XG4gIHByYWNvd25pa19pbWllOiBzdHJpbmcgfCBudWxsO1xuICBwcmFjb3duaWtfbmF6d2lza286IHN0cmluZyB8IG51bGw7XG4gIHN1cm93aWVjX25hendhOiBzdHJpbmcgfCBudWxsO1xuICBtYXRlcmlhbF9uYXp3YTogc3RyaW5nIHwgbnVsbDtcbn1cblxuaW50ZXJmYWNlIFByb2R1a3Qge1xuICBpZDogbnVtYmVyO1xuICBrb2RfaGFuZGxvd3k6IHN0cmluZyB8IG51bGw7XG4gIG5hendhOiBzdHJpbmcgfCBudWxsO1xuICBtZXRyYXo6IG51bWJlcjtcbiAgaW5mbzogc3RyaW5nIHwgbnVsbDtcbn1cblxuaW50ZXJmYWNlIE1hc3p5bmEge1xuICBpZDogbnVtYmVyO1xuICBuYXp3YTogc3RyaW5nO1xuICB0eXA6IHN0cmluZyB8IG51bGw7XG4gIGluZm86IHN0cmluZyB8IG51bGw7XG59XG5cbmludGVyZmFjZSBQcmFjb3duaWsge1xuICBpZDogbnVtYmVyO1xuICBudW1lcjogc3RyaW5nO1xuICBpbWllOiBzdHJpbmcgfCBudWxsO1xuICBuYXp3aXNrbzogc3RyaW5nIHwgbnVsbDtcbiAgc3Rhbm93aXNrbzogc3RyaW5nIHwgbnVsbDtcbn1cblxuaW50ZXJmYWNlIFN1cm93aWVjIHtcbiAgaWQ6IG51bWJlcjtcbiAgbmF6d2E6IHN0cmluZztcbiAgdHlwOiBzdHJpbmcgfCBudWxsO1xuICBpbmZvOiBzdHJpbmcgfCBudWxsO1xufVxuXG5pbnRlcmZhY2UgTWF0ZXJpYWwge1xuICBpZDogbnVtYmVyO1xuICBuYXp3YTogc3RyaW5nO1xuICB0eXA6IHN0cmluZyB8IG51bGw7XG4gIGluZm86IHN0cmluZyB8IG51bGw7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJhcG9ydHlQYWdlKCkge1xuICBjb25zdCBbcmFwb3J0eSwgc2V0UmFwb3J0eV0gPSB1c2VTdGF0ZTxSYXBvcnRQcm9kdWtjeWpueVtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTsgLy8gVGVtcG9yYXJpbHkgZGlzYWJsZSBsb2FkaW5nIHRvIHRlc3RcblxuICBjb25zdCBbcHJvZHVrdHksIHNldFByb2R1a3R5XSA9IHVzZVN0YXRlPFByb2R1a3RbXT4oW10pO1xuICBjb25zdCBbbWFzenlueSwgc2V0TWFzenlueV0gPSB1c2VTdGF0ZTxNYXN6eW5hW10+KFtdKTtcbiAgY29uc3QgW3ByYWNvd25pY3ksIHNldFByYWNvd25pY3ldID0gdXNlU3RhdGU8UHJhY293bmlrW10+KFtdKTtcbiAgY29uc3QgW3N1cm93Y2UsIHNldFN1cm93Y2VdID0gdXNlU3RhdGU8U3Vyb3dpZWNbXT4oW10pO1xuICBjb25zdCBbbWF0ZXJpYWx5LCBzZXRNYXRlcmlhbHldID0gdXNlU3RhdGU8TWF0ZXJpYWxbXT4oW10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbG9hZERhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChbZmV0Y2hSYXBvcnR5KCksIGZldGNoTWFzdGVyRGF0YSgpXSk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiQsWCxIVkIHBvZGN6YXMgxYJhZG93YW5pYSBkYW55Y2g6XCIsIGVycm9yKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG4gICAgbG9hZERhdGEoKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGZldGNoUmFwb3J0eSA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9yYXBvcnR5LWRpcmVjdGApO1xuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIHNldFJhcG9ydHkoZGF0YSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJCxYLEhWQgcG9kY3phcyDFgmFkb3dhbmlhIHJhcG9ydMOzdzpcIiwgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmZXRjaE1hc3RlckRhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IFtwcm9kdWt0eVJlcywgbWFzenlueVJlcywgcHJhY293bmljeVJlcywgc3Vyb3djZVJlcywgbWF0ZXJpYWx5UmVzXSA9XG4gICAgICAgIGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgICAgICBmZXRjaChcIi9hcGkvcHJvZHVrdHktZGlyZWN0XCIpLFxuICAgICAgICAgIGZldGNoKFwiL2FwaS9tYXN6eW55XCIpLFxuICAgICAgICAgIGZldGNoKFwiL2FwaS9wcmFjb3duaWN5XCIpLFxuICAgICAgICAgIGZldGNoKFwiL2FwaS9zdXJvd2NlXCIpLFxuICAgICAgICAgIGZldGNoKFwiL2FwaS9tYXRlcmlhbHlcIiksXG4gICAgICAgIF0pO1xuXG4gICAgICBzZXRQcm9kdWt0eShhd2FpdCBwcm9kdWt0eVJlcy5qc29uKCkpO1xuICAgICAgc2V0TWFzenlueShhd2FpdCBtYXN6eW55UmVzLmpzb24oKSk7XG4gICAgICBzZXRQcmFjb3duaWN5KGF3YWl0IHByYWNvd25pY3lSZXMuanNvbigpKTtcbiAgICAgIHNldFN1cm93Y2UoYXdhaXQgc3Vyb3djZVJlcy5qc29uKCkpO1xuICAgICAgc2V0TWF0ZXJpYWx5KGF3YWl0IG1hdGVyaWFseVJlcy5qc29uKCkpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiQsWCxIVkIHBvZGN6YXMgxYJhZG93YW5pYSBkYW55Y2g6XCIsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRWRpdCA9IChyYXBvcnQ6IFJhcG9ydFByb2R1a2N5am55KSA9PiB7XG4gICAgLy8gUmVkaXJlY3QgdG8gZWRpdCBwYWdlIGluc3RlYWQgb2YgaW5saW5lIGVkaXRpbmdcbiAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IGAvZWR5dHVqLXJhcG9ydC8ke3JhcG9ydC5pZH1gO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZSA9IGFzeW5jIChpZDogbnVtYmVyKSA9PiB7XG4gICAgaWYgKCFjb25maXJtKFwiQ3p5IG5hIHBld25vIGNoY2VzeiB1c3VuxIXEhyB0ZW4gcmFwb3J0P1wiKSkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvcmFwb3J0eS1kaXJlY3QvJHtpZH1gLCB7XG4gICAgICAgIG1ldGhvZDogXCJERUxFVEVcIixcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgYXdhaXQgZmV0Y2hSYXBvcnR5KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiQsWCxIVkIHBvZGN6YXMgdXN1d2FuaWEgcmFwb3J0dVwiKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkLFgsSFZCBwb2RjemFzIHVzdXdhbmlhIHJhcG9ydHU6XCIsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS05MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQteGxcIj5cbiAgICAgICAgICDFgWFkb3dhbmllLi4uIChSZXBvcnRzOiB7cmFwb3J0eS5sZW5ndGh9LCBQcm9kdWN0czoge3Byb2R1a3R5Lmxlbmd0aH0pXG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTkwMCB2aWEtYmx1ZS05MDAgdG8tcHVycGxlLTkwMCBwLTRcIj5cbiAgICAgIHsvKiBTaW1wbGlmaWVkIGJhY2tncm91bmQgKi99XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctZnVsbCBteC1hdXRvIHJlbGF0aXZlIHotMTBcIj5cbiAgICAgICAgey8qIEhlYWRlciBTZWN0aW9uIHdpdGggbW9kZXJuIHN0eWxpbmcgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xhc3MtZGFyayByb3VuZGVkLXQtMnhsIGJvcmRlciBib3JkZXItZ3JheS02MDAvNTAgcC02IGFuaW1hdGUtc2xpZGUtaW4tdG9wIHNoYWRvdy0yeGxcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNlwiPlxuICAgICAgICAgICAgICB7LyogQW5pbWF0ZWQgaWNvbiAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCByb3VuZGVkLXhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGZsb2F0aW5nXCI+XG4gICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICBkPVwiTTkgMTJoNm0tNiA0aDZtMiA1SDdhMiAyIDAgMDEtMi0yVjVhMiAyIDAgMDEyLTJoNS41ODZhMSAxIDAgMDEuNzA3LjI5M2w1LjQxNCA1LjQxNGExIDEgMCAwMS4yOTMuNzA3VjE5YTIgMiAwIDAxLTIgMnpcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgdy00IGgtNCBiZy1ncmVlbi01MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIGJnLWdyYWRpZW50LXRvLXIgZnJvbS13aGl0ZSB0by1ibHVlLTIwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPlxuICAgICAgICAgICAgICAgICAgUkFQT1JUWSBQUk9EVUtDWUpORVxuICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNDAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICBMaXN0YSB3c3p5c3RraWNoIHJhcG9ydMOzd1xuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICB7LyogQ29tcGFueSBsb2dvIHdpdGggZ2xvdyBlZmZlY3QgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1yZWQtNjAwIHRvLXJlZC03MDAgdGV4dC13aGl0ZSBweC02IHB5LTMgcm91bmRlZC14bCBmb250LWJvbGQgdGV4dC1sZyBzaGFkb3ctbGcgYW5pbWF0ZS1nbG93XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctd2hpdGUvMjAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ibGFja1wiPkFJQjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPkVMQVNUT01FUlk8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLXJlZC02MDAgdG8tcmVkLTcwMCByb3VuZGVkLXhsIGJsdXIgb3BhY2l0eS0zMCAtei0xMFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEFjdGlvbiBidXR0b25zICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBocmVmPVwiL25vd3ktcmFwb3J0XCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLW1vZGVybiBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tcHVycGxlLTYwMCBob3Zlcjpmcm9tLWJsdWUtNzAwIGhvdmVyOnRvLXB1cnBsZS03MDAgdGV4dC13aGl0ZSBweC02IHB5LTMgcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6c2hhZG93LXhsIGZvbnQtc2VtaWJvbGQgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNSBoLTVcIlxuICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgZD1cIk0xMiA0djE2bTgtOEg0XCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPHNwYW4+Tm93eSBSYXBvcnQ8L3NwYW4+XG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2ZldGNoUmFwb3J0eX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLW1vZGVybiBnbGFzcyBib3JkZXIgYm9yZGVyLWdyYXktNjAwLzMwIGhvdmVyOmJvcmRlci1ibHVlLTUwMC81MCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBmb250LW1lZGl1bSBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy01IGgtNVwiXG4gICAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICBkPVwiTTQgNHY1aC41ODJtMTUuMzU2IDJBOC4wMDEgOC4wMDEgMCAwMDQuNTgyIDltMCAwSDltMTEgMTF2LTVoLS41ODFtMCAwYTguMDAzIDguMDAzIDAgMDEtMTUuMzU3LTJtMTUuMzU3IDJIMTVcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8c3Bhbj5PZMWbd2llxbw8L3NwYW4+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE1haW4gQ29udGVudCBBcmVhIHdpdGggVGFibGUgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xhc3MtZGFyayBib3JkZXIteCBib3JkZXItYiBib3JkZXItZ3JheS02MDAvNTAgcm91bmRlZC1iLTJ4bCBzaGFkb3ctMnhsIGFuaW1hdGUtZmFkZS1pbi11cFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICB7LyogRGVidWc6IHJhcG9ydHkubGVuZ3RoID0ge3JhcG9ydHkubGVuZ3RofSAqL31cbiAgICAgICAgICAgIHtyYXBvcnR5Lmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xNiBhbmltYXRlLWZhZGUtaW4tdXBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG1iLThcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC04eGwgbWItNCBmbG9hdGluZ1wiPvCfk4o8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ibHVlLTUwMC8yMCByb3VuZGVkLWZ1bGwgYmx1ci0zeGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIHRleHQtMnhsIG1iLTggZm9udC1saWdodFwiPlxuICAgICAgICAgICAgICAgICAgQnJhayByYXBvcnTDs3cgdyBiYXppZSBkYW55Y2hcbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvbm93eS1yYXBvcnRcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIGJ0bi1tb2Rlcm4gYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLXB1cnBsZS02MDAgaG92ZXI6ZnJvbS1ibHVlLTcwMCBob3Zlcjp0by1wdXJwbGUtNzAwIHRleHQtd2hpdGUgcHgtMTAgcHktNCByb3VuZGVkLTJ4bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6c2hhZG93LTJ4bCBmb250LXNlbWlib2xkIHRleHQtbGdcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy01IGgtNVwiXG4gICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTEyIDR2MTZtOC04SDRcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5Eb2RhaiByYXBvcnQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIDx0aGVhZD5cbiAgICAgICAgICAgICAgICAgIDx0ciBjbGFzc05hbWU9XCJiZy1ncmF5LTcwMCBib3JkZXItYiBib3JkZXItZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTIgcHktMyB0ZXh0LWxlZnQgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCB3LTEyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgSURcbiAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTMgcHktMyB0ZXh0LWxlZnQgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCBtaW4tdy1bMTIwcHhdXCI+XG4gICAgICAgICAgICAgICAgICAgICAgS29kIGhhbmRsb3d5XG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC0zIHB5LTMgdGV4dC1sZWZ0IHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBib3JkZXItciBib3JkZXItZ3JheS02MDAgbWluLXctWzIwMHB4XVwiPlxuICAgICAgICAgICAgICAgICAgICAgIE5hendhXG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC0yIHB5LTMgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCB3LTI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgTnIgemFtw7N3aWVuaWFcbiAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTIgcHktMyB0ZXh0LWNlbnRlciB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgYm9yZGVyLXIgYm9yZGVyLWdyYXktNjAwIHctMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICBXYWdhXG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC0yIHB5LTMgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCB3LTIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgTWV0cmHFvCByb2xraVxuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtMiBweS0zIHRleHQtY2VudGVyIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBib3JkZXItciBib3JkZXItZ3JheS02MDAgdy0xNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIElsb8WbxIcgc3p0XG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC0yIHB5LTMgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCB3LTIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgU3VtYSBtZXRyw7N3XG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC0yIHB5LTMgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCB3LTI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgU3Vyb3dpZWMgdcW8eXR5XG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC0yIHB5LTMgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCB3LTI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgU3phcsW8YSBzdXJvd2NhXG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC0yIHB5LTMgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCB3LTI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgWnXFvHljaWUgc3Vyb3djYVxuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtMiBweS0zIHRleHQtY2VudGVyIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBib3JkZXItciBib3JkZXItZ3JheS02MDAgdy0yNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIE9kcGFkIG5pZXXFvHl0a293eVxuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtMiBweS0zIHRleHQtY2VudGVyIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBib3JkZXItciBib3JkZXItZ3JheS02MDAgdy0yMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIE5yIG1hc3p5bnlcbiAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTIgcHktMyB0ZXh0LWNlbnRlciB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgYm9yZGVyLXIgYm9yZGVyLWdyYXktNjAwIHctMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICBJRCBwcmFjb3duaWthXG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC0yIHB5LTMgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCB3LTIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQ3phcyBwcmFjeVxuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtMiBweS0zIHRleHQtY2VudGVyIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCB3LTI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgQWtjamVcbiAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgPC90aGVhZD5cbiAgICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiZ2xhc3NcIj5cbiAgICAgICAgICAgICAgICAgIHtyYXBvcnR5Lm1hcCgocmFwb3J0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8dHJcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e3JhcG9ydC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0YWJsZS1yb3ctaG92ZXIgYm9yZGVyLWIgYm9yZGVyLWdyYXktNzAwLzUwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ibHVlLTkwMC8yMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXggJSAyID09PSAwID8gXCJiZy1ncmF5LTgwMC81MFwiIDogXCJiZy1ncmF5LTc1MC81MFwiXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6IGAke2luZGV4ICogMC4xfXNgIH19XG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRWRpdChyYXBvcnQpfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTIgcHktMiB0ZXh0LWNlbnRlciB0ZXh0LXdoaXRlIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3JhcG9ydC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC13aGl0ZSBib3JkZXItciBib3JkZXItZ3JheS02MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtyYXBvcnQucHJvZHVrdF9rb2QgfHwgXCItXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMyBweS0yIHRleHQtd2hpdGUgYm9yZGVyLXIgYm9yZGVyLWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cmFwb3J0LnByb2R1a3RfbmF6d2EgfHwgXCItXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMiBweS0yIHRleHQtY2VudGVyIHRleHQtd2hpdGUgYm9yZGVyLXIgYm9yZGVyLWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cmFwb3J0Lm5yX3phbW93aWVuaWEgfHwgXCItXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMiBweS0yIHRleHQtY2VudGVyIHRleHQtd2hpdGUgYm9yZGVyLXIgYm9yZGVyLWdyYXktNjAwIGZvbnQtbW9ub1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3JhcG9ydC53YWdhPy50b0ZpeGVkKDEpIHx8IFwiLVwifVxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTIgcHktMiB0ZXh0LWNlbnRlciB0ZXh0LXdoaXRlIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCBmb250LW1vbm9cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtyYXBvcnQubWV0cmF6X3JvbGVrLnRvRml4ZWQoMCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMiBweS0yIHRleHQtY2VudGVyIHRleHQtd2hpdGUgYm9yZGVyLXIgYm9yZGVyLWdyYXktNjAwIGZvbnQtbW9ub1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3JhcG9ydC5pbG9zY19yb2xla31cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC0yIHB5LTIgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZSBib3JkZXItciBib3JkZXItZ3JheS02MDAgZm9udC1tb25vXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7KHJhcG9ydC5tZXRyYXpfcm9sZWsgKiByYXBvcnQuaWxvc2Nfcm9sZWspLnRvRml4ZWQoMCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMiBweS0yIHRleHQtY2VudGVyIHRleHQtd2hpdGUgYm9yZGVyLXIgYm9yZGVyLWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cmFwb3J0LnN1cm93aWVjX25hendhIHx8IFwiLVwifVxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTIgcHktMiB0ZXh0LWNlbnRlciB0ZXh0LXdoaXRlIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3JhcG9ydC5zemFyemFfc3Vyb3djYSB8fCBcIi1cIn1cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC0yIHB5LTIgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZSBib3JkZXItciBib3JkZXItZ3JheS02MDAgZm9udC1tb25vXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cmFwb3J0Lnp1enl0eV9zdXJvd2llYz8udG9GaXhlZCgxKSB8fCBcIjAuMFwifVxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTIgcHktMiB0ZXh0LWNlbnRlciB0ZXh0LXdoaXRlIGJvcmRlci1yIGJvcmRlci1ncmF5LTYwMCBmb250LW1vbm9cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtyYXBvcnQub2RwYWRfc3Vyb3dpZWM/LnRvRml4ZWQoMSkgfHwgXCIwLjBcIn1cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC0yIHB5LTIgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZSBib3JkZXItciBib3JkZXItZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcHgtMSBweS0wLjUgcm91bmRlZCB0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtyYXBvcnQubWFzenluYV9uYXp3YSB8fCBcIi1cIn1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC0yIHB5LTIgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZSBib3JkZXItciBib3JkZXItZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIHB4LTEgcHktMC41IHJvdW5kZWQgdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJhY293bmljeS5maW5kKChwKSA9PiBwLmlkID09PSByYXBvcnQucHJhY293bmlrX2lkKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8ubnVtZXIgfHwgXCItXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMiBweS0yIHRleHQtY2VudGVyIHRleHQtd2hpdGUgYm9yZGVyLXIgYm9yZGVyLWdyYXktNjAwIGZvbnQtbW9ub1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3JhcG9ydC5jemFzX3ByYWN5X21hc3p5bnk/LnRvRml4ZWQoMSkgfHwgXCIwLjBcIn1cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC0yIHB5LTIgdGV4dC1jZW50ZXIgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVFZGl0KHJhcG9ydCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAg4pyP77iPXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEZWxldGUocmFwb3J0LmlkKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXJlZC02MDAgaG92ZXI6YmctcmVkLTcwMCB0ZXh0LXdoaXRlIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+Xke+4j1xuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFN1bW1hcnkgc2VjdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IGdsYXNzLWRhcmsgcm91bmRlZC0yeGwgYm9yZGVyIGJvcmRlci1ncmF5LTYwMC81MCBwLTYgYW5pbWF0ZS1mYWRlLWluLXVwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS00MDBcIj5cbiAgICAgICAgICAgICAgICB7cmFwb3J0eS5sZW5ndGh9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIMWBxIVjem5hIGxpY3piYSByYXBvcnTDs3dcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi00MDBcIj5cbiAgICAgICAgICAgICAgICB7cmFwb3J0eVxuICAgICAgICAgICAgICAgICAgLnJlZHVjZSgoc3VtLCByKSA9PiBzdW0gKyByLm1ldHJhel9yb2xlayAqIHIuaWxvc2Nfcm9sZWssIDApXG4gICAgICAgICAgICAgICAgICAudG9GaXhlZCgwKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCB0ZXh0LXNtXCI+xYHEhWN6bmUgbWV0cnk8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS00MDBcIj5cbiAgICAgICAgICAgICAgICB7cmFwb3J0eS5yZWR1Y2UoKHN1bSwgcikgPT4gc3VtICsgci5pbG9zY19yb2xlaywgMCl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1zbVwiPsWBxIVjem5hIGlsb8WbxIcgc3p0dWs8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LW9yYW5nZS00MDBcIj5cbiAgICAgICAgICAgICAgICB7cmFwb3J0eVxuICAgICAgICAgICAgICAgICAgLnJlZHVjZSgoc3VtLCByKSA9PiBzdW0gKyByLmN6YXNfcHJhY3lfbWFzenlueSwgMClcbiAgICAgICAgICAgICAgICAgIC50b0ZpeGVkKDEpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIHRleHQtc21cIj7FgcSFY3pueSBjemFzIHByYWN5IChoKTwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBOYXZpZ2F0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgZmxleCBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgaHJlZj1cIi9cIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLW1vZGVybiBnbGFzcyBib3JkZXIgYm9yZGVyLWdyYXktNjAwLzMwIGhvdmVyOmJvcmRlci1ibHVlLTUwMC81MCB0ZXh0LXdoaXRlIHB5LTMgcHgtNiByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBmb250LW1lZGl1bSBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxzdmdcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNFwiXG4gICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICBkPVwiTTEwIDE5bC03LTdtMCAwbDctN20tNyA3aDE4XCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPHNwYW4+TWVudSBHxYLDs3duZTwvc3Bhbj5cbiAgICAgICAgICA8L0xpbms+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJMaW5rIiwiUmFwb3J0eVBhZ2UiLCJyYXBvcnR5Iiwic2V0UmFwb3J0eSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwicHJvZHVrdHkiLCJzZXRQcm9kdWt0eSIsIm1hc3p5bnkiLCJzZXRNYXN6eW55IiwicHJhY293bmljeSIsInNldFByYWNvd25pY3kiLCJzdXJvd2NlIiwic2V0U3Vyb3djZSIsIm1hdGVyaWFseSIsInNldE1hdGVyaWFseSIsImxvYWREYXRhIiwiUHJvbWlzZSIsImFsbCIsImZldGNoUmFwb3J0eSIsImZldGNoTWFzdGVyRGF0YSIsImVycm9yIiwiY29uc29sZSIsInJlc3BvbnNlIiwiZmV0Y2giLCJkYXRhIiwianNvbiIsInByb2R1a3R5UmVzIiwibWFzenlueVJlcyIsInByYWNvd25pY3lSZXMiLCJzdXJvd2NlUmVzIiwibWF0ZXJpYWx5UmVzIiwiaGFuZGxlRWRpdCIsInJhcG9ydCIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsImlkIiwiaGFuZGxlRGVsZXRlIiwiY29uZmlybSIsIm1ldGhvZCIsIm9rIiwiZGl2IiwiY2xhc3NOYW1lIiwibGVuZ3RoIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwiaDEiLCJzcGFuIiwiYnV0dG9uIiwib25DbGljayIsInAiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwibWFwIiwiaW5kZXgiLCJzdHlsZSIsImFuaW1hdGlvbkRlbGF5IiwidGQiLCJwcm9kdWt0X2tvZCIsInByb2R1a3RfbmF6d2EiLCJucl96YW1vd2llbmlhIiwid2FnYSIsInRvRml4ZWQiLCJtZXRyYXpfcm9sZWsiLCJpbG9zY19yb2xlayIsInN1cm93aWVjX25hendhIiwic3phcnphX3N1cm93Y2EiLCJ6dXp5dHlfc3Vyb3dpZWMiLCJvZHBhZF9zdXJvd2llYyIsIm1hc3p5bmFfbmF6d2EiLCJmaW5kIiwicHJhY293bmlrX2lkIiwibnVtZXIiLCJjemFzX3ByYWN5X21hc3p5bnkiLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwicmVkdWNlIiwic3VtIiwiciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/raporty/page.tsx\n"));

/***/ })

});