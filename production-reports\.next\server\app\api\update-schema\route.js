/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/update-schema/route";
exports.ids = ["app/api/update-schema/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupdate-schema%2Froute&page=%2Fapi%2Fupdate-schema%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupdate-schema%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupdate-schema%2Froute&page=%2Fapi%2Fupdate-schema%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupdate-schema%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_update_schema_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/update-schema/route.ts */ \"(rsc)/./src/app/api/update-schema/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/update-schema/route\",\n        pathname: \"/api/update-schema\",\n        filename: \"route\",\n        bundlePath: \"app/api/update-schema/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\api\\\\update-schema\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_update_schema_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupdate-schema%2Froute&page=%2Fapi%2Fupdate-schema%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupdate-schema%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/update-schema/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/update-schema/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET() {\n    let db;\n    try {\n        const Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n        const path = __webpack_require__(/*! path */ \"path\");\n        const dbPath = path.join(process.cwd(), \"prisma\", \"baza_danych.db\");\n        db = new Database(dbPath);\n        console.log(\"Updating produkty table schema...\");\n        // Add missing columns to produkty table\n        const columnsToAdd = [\n            \"material_percent REAL\",\n            \"material1 TEXT\",\n            \"material1_percent REAL\",\n            \"material2 TEXT\",\n            \"material2_percent REAL\",\n            \"material3 TEXT\",\n            \"material3_percent REAL\",\n            \"material4 TEXT\",\n            \"material4_percent REAL\",\n            \"material5 TEXT\",\n            \"material5_percent REAL\",\n            \"wydajnosc REAL\"\n        ];\n        for (const column of columnsToAdd){\n            try {\n                db.exec(`ALTER TABLE produkty ADD COLUMN ${column}`);\n                console.log(`Added column: ${column}`);\n            } catch (e) {\n                console.log(`Column might already exist: ${column}`);\n            }\n        }\n        // Check final schema\n        const schema = db.prepare(\"PRAGMA table_info(produkty)\").all();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Schema updated successfully\",\n            columns: schema.map((col)=>`${col.name} (${col.type})`)\n        });\n    } catch (error) {\n        console.error(\"Error updating schema:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Error updating schema\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    } finally{\n        if (db) {\n            db.close();\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/update-schema/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupdate-schema%2Froute&page=%2Fapi%2Fupdate-schema%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupdate-schema%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();