"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/raporty/page",{

/***/ "(app-pages-browser)/./src/app/raporty/page.tsx":
/*!**********************************!*\
  !*** ./src/app/raporty/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RaportyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction RaportyPage() {\n    _s();\n    const [raporty, setRaporty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [produkty, setProdukty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [maszyny, setMaszyny] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pracownicy, setPracownicy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [surowce, setSurowce] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [materialy, setMaterialy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RaportyPage.useEffect\": ()=>{\n            const loadData = {\n                \"RaportyPage.useEffect.loadData\": async ()=>{\n                    try {\n                        await Promise.all([\n                            fetchRaporty(),\n                            fetchMasterData()\n                        ]);\n                    } catch (error) {\n                        console.error(\"Błąd podczas ładowania danych:\", error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"RaportyPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"RaportyPage.useEffect\"], []);\n    const fetchRaporty = async ()=>{\n        try {\n            const response = await fetch(\"/api/raporty-direct\");\n            const data = await response.json();\n            setRaporty(data);\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania raportów:\", error);\n        }\n    };\n    const fetchMasterData = async ()=>{\n        try {\n            const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] = await Promise.all([\n                fetch(\"/api/produkty-direct\"),\n                fetch(\"/api/maszyny\"),\n                fetch(\"/api/pracownicy\"),\n                fetch(\"/api/surowce\"),\n                fetch(\"/api/materialy\")\n            ]);\n            setProdukty(await produktyRes.json());\n            setMaszyny(await maszynyRes.json());\n            setPracownicy(await pracownicyRes.json());\n            setSurowce(await surowceRes.json());\n            setMaterialy(await materialyRes.json());\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania danych:\", error);\n        }\n    };\n    const handleEdit = (raport)=>{\n        // Redirect to edit page instead of inline editing\n        window.location.href = \"/edytuj-raport/\".concat(raport.id);\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm(\"Czy na pewno chcesz usunąć ten raport?\")) return;\n        try {\n            const response = await fetch(\"/api/raporty-direct/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                await fetchRaporty();\n            } else {\n                console.error(\"Błąd podczas usuwania raportu\");\n            }\n        } catch (error) {\n            console.error(\"Błąd podczas usuwania raportu:\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-xl\",\n                children: [\n                    \"Ładowanie... (Reports: \",\n                    raporty.length,\n                    \", Products: \",\n                    produkty.length,\n                    \")\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm mt-2\",\n                        children: [\n                            \"Loading state: \",\n                            loading ? \"true\" : \"false\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-full mx-auto relative z-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-dark rounded-t-2xl border border-gray-600/50 p-6 animate-slide-in-top shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center floating\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                                                    children: \"RAPORTY PRODUKCYJNE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400 font-medium\",\n                                                    children: \"Lista wszystkich raport\\xf3w\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-xl font-bold text-lg shadow-lg animate-glow\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-black\",\n                                                                children: \"AIB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"ELASTOMERY\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-red-600 to-red-700 rounded-xl blur opacity-30 -z-10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/nowy-raport\",\n                                    className: \"btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl transition-all duration-300 hover:shadow-xl font-semibold flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 4v16m8-8H4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Nowy Raport\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchRaporty,\n                                    className: \"btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white px-6 py-3 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Odśwież\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-dark border-x border-b border-gray-600/50 rounded-b-2xl shadow-2xl animate-fade-in-up\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: raporty.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16 animate-fade-in-up\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-8xl mb-4 floating\",\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-blue-500/20 rounded-full blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-2xl mb-8 font-light\",\n                                    children: \"Brak raport\\xf3w w bazie danych\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/nowy-raport\",\n                                    className: \"inline-block btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 rounded-2xl transition-all duration-300 hover:shadow-2xl font-semibold text-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Dodaj raport\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"bg-gray-700 border-b border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-left text-white font-semibold border-r border-gray-600 w-12\",\n                                                children: \"ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[120px]\",\n                                                children: \"Kod handlowy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[200px]\",\n                                                children: \"Nazwa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Nr zam\\xf3wienia\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Waga\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Metraż rolki\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-16\",\n                                                children: \"Ilość szt\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Suma metr\\xf3w\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Surowiec użyty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Szarża surowca\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Zużycie surowca\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                children: \"Odpad nieużytkowy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Nr maszyny\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"ID pracownika\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                children: \"Czas pracy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-2 py-3 text-center text-white font-semibold w-24\",\n                                                children: \"Akcje\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"glass\",\n                                    children: raporty.map((raport, index)=>{\n                                        var _raport_waga, _raport_zuzyty_surowiec, _raport_odpad_surowiec, _pracownicy_find, _raport_czas_pracy_maszyny;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"table-row-hover border-b border-gray-700/50 transition-all duration-300 cursor-pointer hover:bg-blue-900/20 \".concat(index % 2 === 0 ? \"bg-gray-800/50\" : \"bg-gray-750/50\"),\n                                            style: {\n                                                animationDelay: \"\".concat(index * 0.1, \"s\")\n                                            },\n                                            onClick: ()=>handleEdit(raport),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-medium\",\n                                                    children: raport.id\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-3 py-2 text-white border-r border-gray-600 font-medium\",\n                                                    children: raport.produkt_kod || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-3 py-2 text-white border-r border-gray-600\",\n                                                    children: raport.produkt_nazwa || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: raport.nr_zamowienia || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_waga = raport.waga) === null || _raport_waga === void 0 ? void 0 : _raport_waga.toFixed(1)) || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: raport.metraz_rolek.toFixed(0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: raport.ilosc_rolek\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: (raport.metraz_rolek * raport.ilosc_rolek).toFixed(0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: raport.surowiec_nazwa || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: raport.szarza_surowca || \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_zuzyty_surowiec = raport.zuzyty_surowiec) === null || _raport_zuzyty_surowiec === void 0 ? void 0 : _raport_zuzyty_surowiec.toFixed(1)) || \"0.0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_odpad_surowiec = raport.odpad_surowiec) === null || _raport_odpad_surowiec === void 0 ? void 0 : _raport_odpad_surowiec.toFixed(1)) || \"0.0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-600 text-white px-1 py-0.5 rounded text-xs\",\n                                                        children: raport.maszyna_nazwa || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-green-600 text-white px-1 py-0.5 rounded text-xs\",\n                                                        children: ((_pracownicy_find = pracownicy.find((p)=>p.id === raport.pracownik_id)) === null || _pracownicy_find === void 0 ? void 0 : _pracownicy_find.numer) || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                    children: ((_raport_czas_pracy_maszyny = raport.czas_pracy_maszyny) === null || _raport_czas_pracy_maszyny === void 0 ? void 0 : _raport_czas_pracy_maszyny.toFixed(1)) || \"0.0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-2 py-2 text-center text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleEdit(raport);\n                                                                },\n                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                children: \"✏️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleDelete(raport.id);\n                                                                },\n                                                                className: \"bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                children: \"\\uD83D\\uDDD1️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, raport.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 glass-dark rounded-2xl border border-gray-600/50 p-6 animate-fade-in-up\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: raporty.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączna liczba raport\\xf3w\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: raporty.reduce((sum, r)=>sum + r.metraz_rolek * r.ilosc_rolek, 0).toFixed(0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączne metry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: raporty.reduce((sum, r)=>sum + r.ilosc_rolek, 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączna ilość sztuk\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400\",\n                                        children: raporty.reduce((sum, r)=>sum + r.czas_pracy_maszyny, 0).toFixed(1)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Łączny czas pracy (h)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex justify-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white py-3 px-6 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Menu Gł\\xf3wne\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n_s(RaportyPage, \"EhEFf1dryrKzAxJk6kIpxQbWon8=\");\n_c = RaportyPage;\nvar _c;\n$RefreshReg$(_c, \"RaportyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/raporty/page.tsx\n"));

/***/ })

});