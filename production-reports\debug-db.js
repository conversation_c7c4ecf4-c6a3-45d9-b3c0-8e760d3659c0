const Database = require('better-sqlite3');
const db = new Database('baza_danych.db');

console.log('=== Raw report data for ID 5 ===');
const report = db.prepare('SELECT * FROM raporty_produkcyjne WHERE id = 5').get();
console.log(report);

console.log('\n=== All Surowce ===');
const surowce = db.prepare('SELECT * FROM surowce').all();
console.log(surowce);

console.log('\n=== All Maszyny ===');
const maszyny = db.prepare('SELECT * FROM maszyny').all();
console.log(maszyny);

console.log('\n=== Report with JOINs (like API) ===');
const reportWithJoins = db.prepare(`
  SELECT
    r.*,
    p.kod_handlowy as produkt_kod,
    p.nazwa as produkt_nazwa,
    m.nazwa as maszyna_nazwa,
    pr.imie as pracownik_imie,
    pr.nazwi<PERSON> as pracownik_nazwisko,
    s.nazwa as surowiec_nazwa,
    mat.nazwa as material_nazwa
  FROM raporty_produkcyjne r
  LEFT JOIN produkty p ON r.produkt_id = p.id
  LEFT JOIN maszyny m ON r.maszyna_id = m.id
  LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id
  LEFT JOIN surowce s ON r.surowiec_id = s.id
  LEFT JOIN materialy mat ON r.material_id = mat.id
  WHERE r.id = 5
`).get();
console.log(reportWithJoins);

db.close();
