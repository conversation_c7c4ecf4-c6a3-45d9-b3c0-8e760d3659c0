/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/raporty-direct/[id]/route";
exports.ids = ["app/api/raporty-direct/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute&page=%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute&page=%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_raporty_direct_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/raporty-direct/[id]/route.ts */ \"(rsc)/./src/app/api/raporty-direct/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/raporty-direct/[id]/route\",\n        pathname: \"/api/raporty-direct/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/raporty-direct/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\api\\\\raporty-direct\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_raporty_direct_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZyYXBvcnR5LWRpcmVjdCUyRiU1QmlkJTVEJTJGcm91dGUmcGFnZT0lMkZhcGklMkZyYXBvcnR5LWRpcmVjdCUyRiU1QmlkJTVEJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGcmFwb3J0eS1kaXJlY3QlMkYlNUJpZCU1RCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNwcm9kdWtjamElNUNWaWRlb3MlNUNBSUJfUkFQT1JUJTVDcHJvZHVjdGlvbi1yZXBvcnRzJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNwcm9kdWtjamElNUNWaWRlb3MlNUNBSUJfUkFQT1JUJTVDcHJvZHVjdGlvbi1yZXBvcnRzJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUMyRDtBQUN4STtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxccHJvZHVrY2phXFxcXFZpZGVvc1xcXFxBSUJfUkFQT1JUXFxcXHByb2R1Y3Rpb24tcmVwb3J0c1xcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxyYXBvcnR5LWRpcmVjdFxcXFxbaWRdXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9yYXBvcnR5LWRpcmVjdC9baWRdL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvcmFwb3J0eS1kaXJlY3QvW2lkXVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvcmFwb3J0eS1kaXJlY3QvW2lkXS9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXHByb2R1a2NqYVxcXFxWaWRlb3NcXFxcQUlCX1JBUE9SVFxcXFxwcm9kdWN0aW9uLXJlcG9ydHNcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxccmFwb3J0eS1kaXJlY3RcXFxcW2lkXVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute&page=%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/raporty-direct/[id]/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/raporty-direct/[id]/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function PUT(request, { params }) {\n    let db;\n    try {\n        const Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n        const path = __webpack_require__(/*! path */ \"path\");\n        const dbPath = path.join(process.cwd(), \"baza_danych.db\");\n        db = new Database(dbPath);\n        // Check and add missing columns if they don't exist\n        try {\n            const tableInfo = db.prepare(\"PRAGMA table_info(raporty_produkcyjne)\").all();\n            const existingColumns = tableInfo.map((col)=>col.name);\n            if (!existingColumns.includes(\"nr_zamowienia\")) {\n                db.exec(\"ALTER TABLE raporty_produkcyjne ADD COLUMN nr_zamowienia TEXT;\");\n                console.log(\"Added nr_zamowienia column\");\n            }\n            if (!existingColumns.includes(\"waga\")) {\n                db.exec(\"ALTER TABLE raporty_produkcyjne ADD COLUMN waga REAL;\");\n                console.log(\"Added waga column\");\n            }\n            if (!existingColumns.includes(\"szarza_surowca\")) {\n                db.exec(\"ALTER TABLE raporty_produkcyjne ADD COLUMN szarza_surowca TEXT;\");\n                console.log(\"Added szarza_surowca column\");\n            }\n        } catch (alterError) {\n            console.log(\"Note: Could not add columns (they may already exist):\", alterError.message);\n        }\n        const id = parseInt(params.id);\n        const data = await request.json();\n        // Update the report\n        const updateReport = db.prepare(`\n      UPDATE raporty_produkcyjne SET\n        produkt_id = ?,\n        metraz_rolek = ?,\n        ilosc_rolek = ?,\n        nr_zamowienia = ?,\n        waga = ?,\n        surowiec_id = ?,\n        zuzyty_surowiec = ?,\n        odpad_surowiec = ?,\n        szarza_surowca = ?,\n        material_id = ?,\n        zuzyty_material = ?,\n        odpad_material = ?,\n        maszyna_id = ?,\n        pracownik_id = ?,\n        czas_pracy_maszyny = ?,\n        czas_pracy_pracownika = ?,\n        uwagi = ?\n      WHERE id = ?\n    `);\n        const result = updateReport.run(data.produkt_id, data.metraz_rolek, data.ilosc_rolek, data.nr_zamowienia || null, data.waga || null, data.surowiec_id, data.zuzyty_surowiec, data.odpad_surowiec, data.szarza_surowca || null, data.material_id, data.zuzyty_material, data.odpad_material, data.maszyna_id, data.pracownik_id, data.czas_pracy_maszyny, data.czas_pracy_pracownika, data.uwagi || null, id);\n        if (result.changes === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Raport nie został znaleziony\"\n            }, {\n                status: 404\n            });\n        }\n        // Fetch the updated report with related data\n        const updatedReport = db.prepare(`\n      SELECT\n        r.*,\n        p.kod_handlowy as produkt_kod,\n        p.nazwa as produkt_nazwa,\n        m.nazwa as maszyna_nazwa,\n        pr.imie as pracownik_imie,\n        pr.nazwisko as pracownik_nazwisko,\n        s.nazwa as surowiec_nazwa,\n        mat.nazwa as material_nazwa\n      FROM raporty_produkcyjne r\n      LEFT JOIN produkty p ON r.produkt_id = p.id\n      LEFT JOIN maszyny m ON r.maszyna_id = m.id\n      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id\n      LEFT JOIN surowce s ON r.surowiec_id = s.id\n      LEFT JOIN materialy mat ON r.material_id = mat.id\n      WHERE r.id = ?\n    `).get(id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(updatedReport);\n    } catch (error) {\n        console.error(\"Błąd podczas aktualizacji raportu:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Błąd podczas aktualizacji raportu\"\n        }, {\n            status: 500\n        });\n    } finally{\n        if (db) {\n            db.close();\n        }\n    }\n}\nasync function DELETE(request, { params }) {\n    let db;\n    try {\n        const Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n        const path = __webpack_require__(/*! path */ \"path\");\n        const dbPath = path.join(process.cwd(), \"baza_danych.db\");\n        db = new Database(dbPath);\n        // Check and add missing columns if they don't exist (for consistency)\n        try {\n            const tableInfo = db.prepare(\"PRAGMA table_info(raporty_produkcyjne)\").all();\n            const existingColumns = tableInfo.map((col)=>col.name);\n            if (!existingColumns.includes(\"nr_zamowienia\")) {\n                db.exec(\"ALTER TABLE raporty_produkcyjne ADD COLUMN nr_zamowienia TEXT;\");\n                console.log(\"Added nr_zamowienia column\");\n            }\n            if (!existingColumns.includes(\"waga\")) {\n                db.exec(\"ALTER TABLE raporty_produkcyjne ADD COLUMN waga REAL;\");\n                console.log(\"Added waga column\");\n            }\n            if (!existingColumns.includes(\"szarza_surowca\")) {\n                db.exec(\"ALTER TABLE raporty_produkcyjne ADD COLUMN szarza_surowca TEXT;\");\n                console.log(\"Added szarza_surowca column\");\n            }\n        } catch (alterError) {\n            console.log(\"Note: Could not add columns (they may already exist):\", alterError.message);\n        }\n        const id = parseInt(params.id);\n        // Delete the report\n        const deleteReport = db.prepare(`\n      DELETE FROM raporty_produkcyjne WHERE id = ?\n    `);\n        const result = deleteReport.run(id);\n        if (result.changes === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Raport nie został znaleziony\"\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Błąd podczas usuwania raportu:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Błąd podczas usuwania raportu\"\n        }, {\n            status: 500\n        });\n    } finally{\n        if (db) {\n            db.close();\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/raporty-direct/[id]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute&page=%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fraporty-direct%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();