"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/raporty/page",{

/***/ "(app-pages-browser)/./src/app/raporty/page.tsx":
/*!**********************************!*\
  !*** ./src/app/raporty/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RaportyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction RaportyPage() {\n    _s();\n    const [raporty, setRaporty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editingId, setEditingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editData, setEditData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [produkty, setProdukty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [maszyny, setMaszyny] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pracownicy, setPracownicy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [surowce, setSurowce] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [materialy, setMaterialy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RaportyPage.useEffect\": ()=>{\n            fetchRaporty();\n            fetchMasterData();\n        }\n    }[\"RaportyPage.useEffect\"], []);\n    const fetchRaporty = async ()=>{\n        try {\n            const response = await fetch(\"/api/raporty-direct\");\n            const data = await response.json();\n            setRaporty(data);\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania raportów:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchMasterData = async ()=>{\n        try {\n            const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] = await Promise.all([\n                fetch(\"/api/produkty-direct\"),\n                fetch(\"/api/maszyny\"),\n                fetch(\"/api/pracownicy\"),\n                fetch(\"/api/surowce\"),\n                fetch(\"/api/materialy\")\n            ]);\n            setProdukty(await produktyRes.json());\n            setMaszyny(await maszynyRes.json());\n            setPracownicy(await pracownicyRes.json());\n            setSurowce(await surowceRes.json());\n            setMaterialy(await materialyRes.json());\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania danych:\", error);\n        }\n    };\n    const handleEdit = (raport)=>{\n        // Redirect to edit page instead of inline editing\n        window.location.href = \"/edytuj-raport/\".concat(raport.id);\n    };\n    const handleSave = async ()=>{\n        if (!editingId || !editData) return;\n        try {\n            const response = await fetch(\"/api/raporty-direct/\".concat(editingId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(editData)\n            });\n            if (response.ok) {\n                await fetchRaporty();\n                setEditingId(null);\n                setEditData({});\n            } else {\n                console.error(\"Błąd podczas zapisywania raportu\");\n            }\n        } catch (error) {\n            console.error(\"Błąd podczas zapisywania raportu:\", error);\n        }\n    };\n    const handleCancel = ()=>{\n        setEditingId(null);\n        setEditData({});\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm(\"Czy na pewno chcesz usunąć ten raport?\")) return;\n        try {\n            const response = await fetch(\"/api/raporty-direct/\".concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                await fetchRaporty();\n            } else {\n                console.error(\"Błąd podczas usuwania raportu\");\n            }\n        } catch (error) {\n            console.error(\"Błąd podczas usuwania raportu:\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    children: Array.from({\n                        length: 20\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-2 h-2 bg-blue-500 rounded-full opacity-20 animate-pulse-slow\",\n                            style: {\n                                left: \"\".concat(Math.random() * 100, \"%\"),\n                                top: \"\".concat(Math.random() * 100, \"%\"),\n                                animationDelay: \"\".concat(Math.random() * 2, \"s\")\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-dark rounded-2xl p-12 border border-blue-500/20 shadow-2xl animate-fade-in-up relative z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 border-4 border-blue-500/20 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-blue-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-2 left-2 w-12 h-12 border-4 border-transparent border-t-purple-500 rounded-full animate-spin\",\n                                        style: {\n                                            animationDirection: \"reverse\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white mb-2 animate-shimmer\",\n                                        children: \"Ładowanie raport\\xf3w produkcyjnych...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-400 animate-pulse\",\n                                        children: \"Przygotowywanie danych\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 h-2 bg-gray-700 rounded-full overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-shimmer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-10 left-10 w-32 h-32 bg-blue-500/5 rounded-full blur-xl animate-pulse-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 w-24 h-24 bg-purple-500/5 rounded-full blur-xl animate-pulse-slow\",\n                        style: {\n                            animationDelay: \"1s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-1/4 w-40 h-40 bg-cyan-500/5 rounded-full blur-xl animate-pulse-slow\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-full mx-auto relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-dark rounded-t-2xl border border-gray-600/50 p-6 animate-slide-in-top shadow-2xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center floating\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6 text-white\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                                                        children: \"RAPORTY PRODUKCYJNE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-400 font-medium\",\n                                                        children: \"Lista wszystkich raport\\xf3w\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-xl font-bold text-lg shadow-lg animate-glow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-black\",\n                                                                    children: \"AIB\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"ELASTOMERY\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-red-600 to-red-700 rounded-xl blur opacity-30 -z-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/nowy-raport\",\n                                        className: \"btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl transition-all duration-300 hover:shadow-xl font-semibold flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Nowy Raport\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: fetchRaporty,\n                                        className: \"btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white px-6 py-3 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Odśwież\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-dark border-x border-b border-gray-600/50 rounded-b-2xl shadow-2xl animate-fade-in-up\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: raporty.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-16 animate-fade-in-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-8xl mb-4 floating\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-blue-500/20 rounded-full blur-3xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 text-2xl mb-8 font-light\",\n                                        children: \"Brak raport\\xf3w w bazie danych\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/nowy-raport\",\n                                        className: \"inline-block btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 rounded-2xl transition-all duration-300 hover:shadow-2xl font-semibold text-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 4v16m8-8H4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Dodaj raport\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-gray-700 border-b border-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-left text-white font-semibold border-r border-gray-600 w-12\",\n                                                    children: \"ID\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[120px]\",\n                                                    children: \"Kod handlowy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-3 py-3 text-left text-white font-semibold border-r border-gray-600 min-w-[200px]\",\n                                                    children: \"Nazwa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                    children: \"Nr zam\\xf3wienia\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"Waga\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"Metraż rolki\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-16\",\n                                                    children: \"Ilość szt\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"Suma metr\\xf3w\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                    children: \"Surowiec użyty\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                    children: \"Szarża surowca\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                    children: \"Zużycie surowca\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-24\",\n                                                    children: \"Odpad nieużytkowy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"Nr maszyny\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"ID pracownika\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold border-r border-gray-600 w-20\",\n                                                    children: \"Czas pracy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-2 py-3 text-center text-white font-semibold w-24\",\n                                                    children: \"Akcje\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"glass\",\n                                        children: raporty.map((raport, index)=>{\n                                            var _raport_waga, _raport_zuzyty_surowiec, _raport_odpad_surowiec, _pracownicy_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"table-row-hover border-b border-gray-700/50 transition-all duration-300 cursor-pointer hover:bg-blue-900/20 \".concat(index % 2 === 0 ? \"bg-gray-800/50\" : \"bg-gray-750/50\", \" \").concat(editingId === raport.id ? \"bg-blue-900/30\" : \"\"),\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 0.1, \"s\")\n                                                },\n                                                onClick: ()=>handleEdit(raport),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-medium\",\n                                                        children: raport.id\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-3 py-2 text-white border-r border-gray-600 font-medium\",\n                                                        children: raport.produkt_kod || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-3 py-2 text-white border-r border-gray-600\",\n                                                        children: raport.produkt_nazwa || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                        children: raport.nr_zamowienia || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: ((_raport_waga = raport.waga) === null || _raport_waga === void 0 ? void 0 : _raport_waga.toFixed(1)) || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: raport.metraz_rolek.toFixed(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: raport.ilosc_rolek\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: (raport.metraz_rolek * raport.ilosc_rolek).toFixed(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                        children: raport.surowiec_nazwa || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                        children: raport.szarza_surowca || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: ((_raport_zuzyty_surowiec = raport.zuzyty_surowiec) === null || _raport_zuzyty_surowiec === void 0 ? void 0 : _raport_zuzyty_surowiec.toFixed(1)) || \"0.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: ((_raport_odpad_surowiec = raport.odpad_surowiec) === null || _raport_odpad_surowiec === void 0 ? void 0 : _raport_odpad_surowiec.toFixed(1)) || \"0.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                        children: editingId === raport.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: editData.maszyna_id || \"\",\n                                                            onChange: (e)=>setEditData({\n                                                                    ...editData,\n                                                                    maszyna_id: parseInt(e.target.value)\n                                                                }),\n                                                            className: \"w-full bg-gray-700 text-white text-xs p-1 rounded\",\n                                                            onClick: (e)=>e.stopPropagation(),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"Wybierz maszynę\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                maszyny.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: m.id,\n                                                                        children: m.nazwa\n                                                                    }, m.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 31\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-blue-600 text-white px-1 py-0.5 rounded text-xs\",\n                                                            children: raport.maszyna_nazwa || \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600\",\n                                                        children: editingId === raport.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: editData.pracownik_id || \"\",\n                                                            onChange: (e)=>setEditData({\n                                                                    ...editData,\n                                                                    pracownik_id: parseInt(e.target.value)\n                                                                }),\n                                                            className: \"w-full bg-gray-700 text-white text-xs p-1 rounded\",\n                                                            onClick: (e)=>e.stopPropagation(),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"Wybierz pracownika\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                pracownicy.map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: p.id,\n                                                                        children: p.numer\n                                                                    }, p.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 31\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-green-600 text-white px-1 py-0.5 rounded text-xs\",\n                                                            children: ((_pracownicy_find = pracownicy.find((p)=>p.id === raport.pracownik_id)) === null || _pracownicy_find === void 0 ? void 0 : _pracownicy_find.numer) || \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white border-r border-gray-600 font-mono\",\n                                                        children: editingId === raport.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: editData.czas_pracy_maszyny || \"\",\n                                                            onChange: (e)=>setEditData({\n                                                                    ...editData,\n                                                                    czas_pracy_maszyny: parseFloat(e.target.value)\n                                                                }),\n                                                            className: \"w-full bg-gray-700 text-white text-xs p-1 rounded\",\n                                                            onClick: (e)=>e.stopPropagation()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 27\n                                                        }, this) : raport.czas_pracy_maszyny.toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-2 py-2 text-center text-white\",\n                                                        children: editingId === raport.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleSave();\n                                                                    },\n                                                                    className: \"bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                    children: \"✓\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleCancel();\n                                                                    },\n                                                                    className: \"bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                    children: \"✕\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleEdit(raport);\n                                                                    },\n                                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                    children: \"✏️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleDelete(raport.id);\n                                                                    },\n                                                                    className: \"bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs transition-colors\",\n                                                                    children: \"\\uD83D\\uDDD1️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, raport.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 glass-dark rounded-2xl border border-gray-600/50 p-6 animate-fade-in-up\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-400\",\n                                            children: raporty.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Łączna liczba raport\\xf3w\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-400\",\n                                            children: raporty.reduce((sum, r)=>sum + r.metraz_rolek * r.ilosc_rolek, 0).toFixed(0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Łączne metry\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-400\",\n                                            children: raporty.reduce((sum, r)=>sum + r.ilosc_rolek, 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Łączna ilość sztuk\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-400\",\n                                            children: raporty.reduce((sum, r)=>sum + r.czas_pracy_maszyny, 0).toFixed(1)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Łączny czas pracy (h)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 flex justify-center space-x-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white py-3 px-6 rounded-xl transition-all duration-300 font-medium flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Menu Gł\\xf3wne\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\raporty\\\\page.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\n_s(RaportyPage, \"/rZCDBvVvVupbfHN1C6Yp/3JlWw=\");\n_c = RaportyPage;\nvar _c;\n$RefreshReg$(_c, \"RaportyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/raporty/page.tsx\n"));

/***/ })

});