import { NextResponse } from "next/server";

export async function POST() {
  let db;

  try {
    const Database = require("better-sqlite3");
    const path = require("path");

    const dbPath = path.join(process.cwd(), "baza_danych.db");
    db = new Database(dbPath);

    // Add missing surowiec "Elastlok 38km"
    const insertSurowiec = db.prepare(`
      INSERT INTO surowce (nazwa, typ, info) 
      VALUES (?, ?, ?)
    `);
    
    try {
      insertSurowiec.run("Elastlok 38km", "Podstawowy", "Surowiec Elastlok 38km");
      console.log("Added Elastlok 38km");
    } catch (e) {
      console.log("Elastlok 38km already exists or error:", e.message);
    }

    // Add missing machine "M31"
    const insertMaszyna = db.prepare(`
      INSERT INTO maszyny (nazwa, typ, info) 
      VALUES (?, ?, ?)
    `);
    
    try {
      insertMaszyna.run("M31", "<PERSON>du<PERSON><PERSON><PERSON><PERSON>", "Maszyna produkcyjna M31");
      console.log("Added M31");
    } catch (e) {
      console.log("M31 already exists or error:", e.message);
    }

    // Get updated lists
    const surowce = db.prepare('SELECT * FROM surowce').all();
    const maszyny = db.prepare('SELECT * FROM maszyny').all();

    return NextResponse.json({
      message: "Missing data added successfully",
      surowce,
      maszyny
    });
  } catch (error) {
    console.error("Error adding missing data:", error);
    return NextResponse.json(
      { error: "Error adding missing data", details: error.message },
      { status: 500 }
    );
  } finally {
    if (db) {
      db.close();
    }
  }
}
