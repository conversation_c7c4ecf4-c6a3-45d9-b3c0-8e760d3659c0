/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/debug-db/route";
exports.ids = ["app/api/debug-db/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebug-db%2Froute&page=%2Fapi%2Fdebug-db%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug-db%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebug-db%2Froute&page=%2Fapi%2Fdebug-db%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug-db%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_debug_db_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/debug-db/route.ts */ \"(rsc)/./src/app/api/debug-db/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/debug-db/route\",\n        pathname: \"/api/debug-db\",\n        filename: \"route\",\n        bundlePath: \"app/api/debug-db/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\api\\\\debug-db\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_debug_db_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebug-db%2Froute&page=%2Fapi%2Fdebug-db%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug-db%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/debug-db/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/debug-db/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET() {\n    let db;\n    try {\n        const Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n        const path = __webpack_require__(/*! path */ \"path\");\n        const dbPath = path.join(process.cwd(), \"baza_danych.db\");\n        db = new Database(dbPath);\n        // Get raw report data\n        const rawReport = db.prepare('SELECT * FROM raporty_produkcyjne WHERE id = 5').get();\n        // Get all surowce\n        const surowce = db.prepare('SELECT * FROM surowce').all();\n        // Get all maszyny\n        const maszyny = db.prepare('SELECT * FROM maszyny').all();\n        // Get report with joins (like the main API)\n        const reportWithJoins = db.prepare(`\n      SELECT\n        r.*,\n        p.kod_handlowy as produkt_kod,\n        p.nazwa as produkt_nazwa,\n        m.nazwa as maszyna_nazwa,\n        pr.imie as pracownik_imie,\n        pr.nazwisko as pracownik_nazwisko,\n        s.nazwa as surowiec_nazwa,\n        mat.nazwa as material_nazwa\n      FROM raporty_produkcyjne r\n      LEFT JOIN produkty p ON r.produkt_id = p.id\n      LEFT JOIN maszyny m ON r.maszyna_id = m.id\n      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id\n      LEFT JOIN surowce s ON r.surowiec_id = s.id\n      LEFT JOIN materialy mat ON r.material_id = mat.id\n      WHERE r.id = 5\n    `).get();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            rawReport,\n            surowce,\n            maszyny,\n            reportWithJoins\n        });\n    } catch (error) {\n        console.error(\"Debug error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Debug error\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    } finally{\n        if (db) {\n            db.close();\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/debug-db/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebug-db%2Froute&page=%2Fapi%2Fdebug-db%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug-db%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();